---
description: 
globs: 
alwaysApply: true
---
- the required tvOS SDK version is 18.0. Try to avoid deprecated APIs.
- the project should follow MVVM pattern.
- when refactoring, consider followings:
    - encapsulation
    - dependency injection for better testability
    - error handling
    - resue the UI components in UI/Components folder
    - separate view component from each View to UI/Components with newly created file if needed.
    - Add Previews
    - Add unit test