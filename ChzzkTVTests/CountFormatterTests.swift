import XCTest
@testable import ChzzkTV

final class CountFormatterTests: XCTestCase {

    func testAbbreviatedUnderThousand() {
        XCTAssertEqual(CountFormatter.abbreviated(from: 0), "0")
        XCTAssertEqual(CountFormatter.abbreviated(from: 1), "1")
        XCTAssertEqual(CountFormatter.abbreviated(from: 999), "999")
    }

    func testAbbreviatedThousandsWithDecimal() {
        XCTAssertEqual(CountFormatter.abbreviated(from: 1200), "1.2K")
        XCTAssertEqual(CountFormatter.abbreviated(from: 9999), "10.0K")
    }

    func testAbbreviatedThousandsRounded() {
        XCTAssertEqual(CountFormatter.abbreviated(from: 12_345), "12K")
        XCTAssertEqual(CountFormatter.abbreviated(from: 987_654), "988K")
    }

    func testAbbreviatedMillions() {
        XCTAssertEqual(CountFormatter.abbreviated(from: 1_000_000), "1.0M")
        XCTAssertEqual(CountFormatter.abbreviated(from: 1_234_567), "1.2M")
    }

    func testKoreanLocaleBehaviorMatchesOriginalLogic() {
        let korean = Locale(identifier: "ko_KR")
        // 12,345 -> original logic was dividing by 10 for ko before formatting 0-decimal K → "1K"
        XCTAssertEqual(CountFormatter.abbreviated(from: 12_345, locale: korean), "1K")
        // 100,000 -> (100k / 1k)/10 = 10 → "10K"
        XCTAssertEqual(CountFormatter.abbreviated(from: 100_000, locale: korean), "10K")
    }

    func testUIChannelFollowerCountFormatted() {
        let channel = UIChannel(id: "1", name: "test", imageUrl: nil, followerCount: 12_345)
        XCTAssertEqual(channel.followerCountFormatted, "12K followers")
    }

    func testUILiveStreamViewerCountFormatted() {
        let stream = UILiveStream(id: 1, title: "t", imageUrl: nil, viewerCount: 9_999, timestamp: 0, category: nil, channel: nil)
        XCTAssertEqual(stream.viewerCountFormatted, "10.0K viewers")
    }

    func testUICategoryViewerCountFormatted() {
        let category = UICategory(id: "g", type: "game", value: "game", imageUrl: nil, channelCount: 123, viewerCount: 1_234_567, isNew: false)
        XCTAssertEqual(category.viewerCountFormatted, "1.2M viewers")
    }
} 