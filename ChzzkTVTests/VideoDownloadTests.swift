//
//  VideoDownloadTests.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import XCTest
@testable import ChzzkTV

final class VideoDownloadTests: XCTestCase {
    var downloadService: VideoDownloadService!
    var mockVideoService: MockVideoService!
    
    override func setUpWithError() throws {
        mockVideoService = MockVideoService()
        downloadService = VideoDownloadService(videoService: mockVideoService)
    }
    
    override func tearDownWithError() throws {
        downloadService = nil
        mockVideoService = nil
    }
    
    func testDownloadVideoCreatesCorrectInfo() async throws {
        // Given
        let video = createTestVideo()
        let quality = createTestQuality()
        
        // When
        let downloadURL = try await downloadService.downloadVideo(video, quality: quality)
        
        // Then
        XCTAssertNotNil(downloadURL)
        XCTAssertTrue(downloadURL.pathExtension == "mp4")
        
        // Verify download was tracked
        let downloadState = downloadService.getDownloadProgress(for: video.id)
        XCTAssertNotNil(downloadState)
    }
    
    func testCancelDownloadRemovesFromActive() {
        // Given
        let video = createTestVideo()
        let quality = createTestQuality()
        
        // When
        Task {
            try await downloadService.downloadVideo(video, quality: quality)
        }
        
        // Cancel immediately
        downloadService.cancelDownload(for: video.id)
        
        // Then
        let downloadState = downloadService.getDownloadProgress(for: video.id)
        if case .cancelled = downloadState {
            // Expected
        } else {
            XCTFail("Download should be cancelled")
        }
    }
    
    func testURLExtractorProcessesHLSPlaylist() async throws {
        // Given
        let extractor = VideoDownloadURLExtractor()
        let hlsURL = URL(string: "https://example.com/playlist.m3u8")!
        let quality = VideoQuality(id: "test", resolution: 1080, bandwidth: 5000000, url: hlsURL)
        
        // When/Then - This would require mocking URLSession for a proper test
        // For now, just verify the extractor can be created
        XCTAssertNotNil(extractor)
    }
    
    // MARK: - Helper Methods
    
    private func createTestVideo() -> UIVideo {
        return UIVideo(
            id: 12345,
            uid: "test-uid",
            title: "Test Video",
            imageUrl: URL(string: "https://example.com/thumb.jpg"),
            duration: 3600,
            timestamp: Date().timeIntervalSince1970,
            category: "Gaming",
            channel: UIChannel(
                id: "test-channel",
                name: "Test Channel",
                imageUrl: URL(string: "https://example.com/channel.jpg")
            ),
            progress: nil
        )
    }
    
    private func createTestQuality() -> VideoQuality {
        return VideoQuality(
            id: "1080p",
            resolution: 1080,
            bandwidth: 5000000,
            url: URL(string: "https://example.com/video.m3u8")!
        )
    }
}

// MARK: - Mock Video Service

class MockVideoService: VideoServiceProtocol {
    func getVideo(_ videoId: Int) async throws -> UIVideo {
        return UIVideo(
            id: videoId,
            uid: "mock-uid",
            title: "Mock Video",
            imageUrl: URL(string: "https://example.com/thumb.jpg"),
            duration: 1800,
            timestamp: Date().timeIntervalSince1970,
            category: "Test",
            channel: nil,
            progress: nil
        )
    }
    
    func getFollowingVideos() async throws -> [UIVideo] {
        return []
    }
    
    func createAsset(for videoId: Int, quality: VideoQuality) async throws -> AVURLAsset {
        return AVURLAsset(url: quality.url)
    }
    
    func clear() {
        // Mock implementation
    }
    
    func watchEvent(time: Int, video: UIVideo, event: VideoPlayEvent) async throws {
        // Mock implementation
    }
    
    func getAvailableQualities(for videoId: Int) async throws -> [VideoQuality] {
        return [
            VideoQuality(id: "1080p", resolution: 1080, bandwidth: 5000000, url: URL(string: "https://example.com/1080p.m3u8")!),
            VideoQuality(id: "720p", resolution: 720, bandwidth: 3000000, url: URL(string: "https://example.com/720p.m3u8")!),
            VideoQuality(id: "480p", resolution: 480, bandwidth: 1500000, url: URL(string: "https://example.com/480p.m3u8")!)
        ]
    }
}
