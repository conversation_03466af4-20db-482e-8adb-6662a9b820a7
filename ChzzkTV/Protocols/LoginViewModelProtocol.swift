//
//  LoginViewModelProtocol.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/28/25.
//

import Foundation
import SwiftUI

// MARK: - User Profile Protocol

protocol UserProfileProtocol {
    var nickname: String { get }
    var profileImageUrl: String? { get }
}

// MARK: - Login View Model Protocol

@MainActor
protocol LoginViewModelProtocol: ObservableObject {
    var isUserLoggedIn: Bool { get set }
    var profileNickname: String? { get }
    var profileImageUrl: String? { get }

    func verifyUserStatus(forValidation: Bool) async
    func signOut()
}

// MARK: - QRCodeLoginViewModel UserProfile Conformance

extension QRCodeLoginViewModel.UserProfile: UserProfileProtocol {}

// MARK: - QRCodeLoginViewModel Protocol Conformance

extension QRCodeLoginViewModel: LoginViewModelProtocol {
    var profileNickname: String? {
        return userProfile?.nickname
    }

    var profileImageUrl: String? {
        return userProfile?.profileImageUrl
    }
}

// MARK: - Mock Implementation for Previews

@MainActor
class MockLoginViewModel: LoginViewModelProtocol {
    @Published var isUserLoggedIn: Bool
    private var mockProfile: MockUserProfile?

    var profileNickname: String? {
        return mockProfile?.nickname
    }

    var profileImageUrl: String? {
        return mockProfile?.profileImageUrl
    }

    init(isLoggedIn: Bool = true, userProfile: MockUserProfile? = nil) {
        self.isUserLoggedIn = isLoggedIn
        self.mockProfile = userProfile ?? MockUserProfile(nickname: "Test User", profileImageUrl: nil)
    }

    func verifyUserStatus(forValidation: Bool = false) async {
        // Mock implementation - do nothing
    }

    func signOut() {
        isUserLoggedIn = false
        mockProfile = nil
    }
}

// MARK: - Mock User Profile

struct MockUserProfile: UserProfileProtocol {
    let nickname: String
    let profileImageUrl: String?
}
