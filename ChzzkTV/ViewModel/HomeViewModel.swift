import Foundation
import SwiftData
import SwiftUI
import os.log

@MainActor
class HomeViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var state: LoadingState = .idle
    @Published var followerLives: [UILiveStream] = []
    @Published var followers: [UIChannel] = []
    @Published var followerVideos: [UIVideo] = []

    // MARK: - Private Properties for Change Detection
    private var lastLoadTime: Date?
    private var cachedFollowerLives: [UILiveStream] = []
    private var cachedFollowers: [UIChannel] = []
    private var cachedFollowerVideos: [UIVideo] = []
    private var loadContentTask: Task<Void, Never>?
    private var isLoadingContent = false // Flag to prevent concurrent loads

    private let logger = Logger(subsystem: "ChzzkTV", category: "HomeViewModel")

    // MARK: - Dependencies
    private let channelService: ChannelServiceProtocol
    private let liveService: LiveServiceProtocol
    private let videoService: VideoServiceProtocol
    let loginViewModel: any LoginViewModelProtocol
    
    // MARK: - State Enum
    enum LoadingState {
        case idle
        case loading
        case loaded
        case error(Error)
        
        var isLoading: Bool {
            if case .loading = self { return true }
            return false
        }
        
        var error: Error? {
            if case .error(let error) = self { return error }
            return nil
        }
    }
    
    // MARK: - Initialization
    init(
        channelService: ChannelServiceProtocol,
        liveService: LiveServiceProtocol,
        videoService: VideoServiceProtocol,
        loginViewModel: any LoginViewModelProtocol
    ) {
        self.channelService = channelService
        self.liveService = liveService
        self.videoService = videoService
        self.loginViewModel = loginViewModel

        // Load cached data immediately when view model is created
        loadCachedDataImmediately()
    }
    
    // MARK: - Public Methods
    func loadContent(forceRefresh: Bool = false) async {
        // Only cancel existing task if it hasn't started yet or if we're forcing a refresh
        if let existingTask = loadContentTask, !existingTask.isCancelled {
            if forceRefresh {
                logger.info("Force refresh requested, cancelling existing load task")
                existingTask.cancel()
            } else if case .loading = state {
                // If already loading, wait for existing task instead of cancelling
                logger.info("Content already loading, waiting for existing task")
                await existingTask.value
                return
            }
        }

        loadContentTask = Task { @MainActor in
            await performLoadContent(forceRefresh: forceRefresh)
        }

        await loadContentTask?.value
    }

    private func performLoadContent(forceRefresh: Bool = false) async {
        // Prevent concurrent loading
        if isLoadingContent && !forceRefresh {
            logger.info("Content loading already in progress, skipping duplicate request")
            return
        }

        isLoadingContent = true
        defer { isLoadingContent = false }

        // Always show cached data first if available (don't change state to loading if we have data)
        let hasCachedData = !cachedFollowerLives.isEmpty || !cachedFollowers.isEmpty || !cachedFollowerVideos.isEmpty
        if hasCachedData {
            followerLives = cachedFollowerLives
            followers = cachedFollowers
            followerVideos = cachedFollowerVideos
            if case .idle = state {
                state = .loaded
            }
        }

        // Only show loading state if we don't have cached data
        if !hasCachedData {
            state = .loading
        }

        do {
            // First, verify user status to ensure cookies are updated
            await loginViewModel.verifyUserStatus(forValidation: true)

            // Only proceed with API calls if user is logged in
            guard loginViewModel.isUserLoggedIn else {
                state = .error(NSError(domain: "ChzzkTV", code: 401, userInfo: [NSLocalizedDescriptionKey: "User not logged in"]))
                return
            }

            async let livesTask = liveService.getFollowingLives()
            async let videosTask = videoService.getFollowingVideos()
            async let channelsTask = channelService.getFollowingChannels()

            let (newLives, newVideos, newChannels) = try await (livesTask, videosTask, channelsTask)

            // Check if data has actually changed before updating UI
            let livesChanged = !arraysEqual(newLives, cachedFollowerLives)
            let videosChanged = !arraysEqual(newVideos, cachedFollowerVideos)
            let channelsChanged = !arraysEqual(newChannels, cachedFollowers)

            if livesChanged || videosChanged || channelsChanged || forceRefresh {
                logger.debug("Data changed - updating UI")

                // Update cached data
                cachedFollowerLives = newLives
                cachedFollowerVideos = newVideos
                cachedFollowers = newChannels

                // Save to persistent storage
                saveCachedDataToStorage()

                // Update published properties
                followerLives = newLives
                followerVideos = newVideos
                followers = newChannels

                // Preload channel images for caching
                await preloadChannelImages()
            } else {
                logger.debug("No data changes detected - keeping current state")

                // Even if data hasn't changed, always update FollowingLives to refresh images
                // This ensures live stream thumbnails are always fresh
                if !newLives.isEmpty {
                    logger.debug("Refreshing FollowingLives images even though data hasn't changed")
                    followerLives = newLives

                    // Force refresh images for live streams by clearing cache for these specific URLs
                    await refreshFollowingLivesImages(newLives)
                }
            }

            lastLoadTime = Date()
            state = .loaded
        } catch {
            // Check if the error is due to cancellation
            if let urlError = error as? URLError, urlError.code == .cancelled {
                logger.info("Content loading was cancelled, not updating error state")
                // Don't update state for cancelled requests - keep current state
                return
            }

            logger.error("Failed to load content: \(error.localizedDescription)")
            // Only show error state if we don't have cached data to fall back on
            let hasCachedData = !cachedFollowerLives.isEmpty || !cachedFollowers.isEmpty || !cachedFollowerVideos.isEmpty
            if !hasCachedData {
                state = .error(error)
            } else {
                // If we have cached data, keep showing it and just log the error
                logger.info("Keeping cached data visible despite error: \(error.localizedDescription)")
            }
        }
    }
    
    func loadVideos() async {
        do {
            // Verify user status before loading videos
            await loginViewModel.verifyUserStatus(forValidation: true)

            guard loginViewModel.isUserLoggedIn else {
                print("User not logged in, skipping video load")
                return
            }

            followerVideos = try await videoService.getFollowingVideos()
        } catch {
            // Check if the error is due to cancellation
            if let urlError = error as? URLError, urlError.code == .cancelled {
                logger.info("Video loading was cancelled")
                return
            }
            logger.error("Failed to load videos: \(error.localizedDescription)")
        }
    }
    
    var isEmpty: Bool {
        followers.isEmpty && followerLives.isEmpty && followerVideos.isEmpty
    }
    
    func unfollow(_ channel: UIChannel) {
        followers = followers.filter({ $0.id != channel.id })
        followerLives = followerLives.filter({ $0.channel?.id != channel.id })
        followerVideos = followerVideos.filter({ $0.channel?.id != channel.id })

        // Update cached data as well
        cachedFollowers = cachedFollowers.filter({ $0.id != channel.id })
        cachedFollowerLives = cachedFollowerLives.filter({ $0.channel?.id != channel.id })
        cachedFollowerVideos = cachedFollowerVideos.filter({ $0.channel?.id != channel.id })
    }

    // MARK: - Private Methods

    /// Loads cached data immediately when the view model is initialized
    private func loadCachedDataImmediately() {
        // Load from persistent storage first
        loadCachedDataFromStorage()

        // This method runs synchronously to show cached data instantly
        if !cachedFollowerLives.isEmpty || !cachedFollowers.isEmpty || !cachedFollowerVideos.isEmpty {
            followerLives = cachedFollowerLives
            followers = cachedFollowers
            followerVideos = cachedFollowerVideos
            state = .loaded
            logger.debug("Loaded cached data immediately on initialization")
        }
    }

    /// Saves cached data to persistent storage
    private func saveCachedDataToStorage() {
        do {
            let livesData = try JSONEncoder().encode(cachedFollowerLives)
            let followersData = try JSONEncoder().encode(cachedFollowers)
            let videosData = try JSONEncoder().encode(cachedFollowerVideos)

            UserDefaults.standard.set(livesData, forKey: "cachedFollowerLives")
            UserDefaults.standard.set(followersData, forKey: "cachedFollowers")
            UserDefaults.standard.set(videosData, forKey: "cachedFollowerVideos")
            UserDefaults.standard.set(Date(), forKey: "cacheTimestamp")

            logger.debug("Saved cached data to storage")
        } catch {
            logger.error("Failed to save cached data: \(error.localizedDescription)")
        }
    }

    /// Loads cached data from persistent storage
    private func loadCachedDataFromStorage() {
        // Check if cached data is not too old (24 hours)
        if let cacheTimestamp = UserDefaults.standard.object(forKey: "cacheTimestamp") as? Date,
           Date().timeIntervalSince(cacheTimestamp) > 24 * 60 * 60 {
            logger.debug("Cached data is too old, clearing it")
            clearCachedDataFromStorage()
            return
        }

        do {
            if let livesData = UserDefaults.standard.data(forKey: "cachedFollowerLives") {
                cachedFollowerLives = try JSONDecoder().decode([UILiveStream].self, from: livesData)
            }

            if let followersData = UserDefaults.standard.data(forKey: "cachedFollowers") {
                cachedFollowers = try JSONDecoder().decode([UIChannel].self, from: followersData)
            }

            if let videosData = UserDefaults.standard.data(forKey: "cachedFollowerVideos") {
                cachedFollowerVideos = try JSONDecoder().decode([UIVideo].self, from: videosData)
            }

            logger.debug("Loaded cached data from storage")
        } catch {
            logger.error("Failed to load cached data: \(error.localizedDescription)")
            clearCachedDataFromStorage()
        }
    }

    /// Clears cached data from persistent storage
    private func clearCachedDataFromStorage() {
        UserDefaults.standard.removeObject(forKey: "cachedFollowerLives")
        UserDefaults.standard.removeObject(forKey: "cachedFollowers")
        UserDefaults.standard.removeObject(forKey: "cachedFollowerVideos")
        UserDefaults.standard.removeObject(forKey: "cacheTimestamp")
        logger.debug("Cleared cached data from storage")
    }

    /// Compares two arrays for equality based on their identifiable elements
    private func arraysEqual<T: Identifiable & Equatable>(_ lhs: [T], _ rhs: [T]) -> Bool {
        guard lhs.count == rhs.count else { return false }

        for (index, item) in lhs.enumerated() {
            if index >= rhs.count || item != rhs[index] {
                return false
            }
        }
        return true
    }

    /// Preloads channel images for better caching
    private func preloadChannelImages() async {
        let allChannels = Set(
            followers +
            followerLives.compactMap { $0.channel } +
            followerVideos.compactMap { $0.channel }
        )

        await withTaskGroup(of: Void.self) { group in
            for channel in allChannels {
                if let imageUrl = channel.imageUrl {
                    group.addTask {
                        _ = await ImageCacheManager.shared.downloadAndCacheImage(from: imageUrl)
                    }
                }
            }
        }

        logger.debug("Preloaded \(allChannels.count) channel images")
    }

    /// Forces refresh of images for FollowingLives by clearing cache and re-downloading
    private func refreshFollowingLivesImages(_ lives: [UILiveStream]) async {
        // Collect all image URLs from live streams (thumbnails and channel images)
        var imageUrls: Set<URL> = Set()

        for live in lives {
            // Add thumbnail URLs (UILiveStream conforms to Thumbnail protocol)
            if let thumbnailUrl = live.imageUrl {
                imageUrls.insert(thumbnailUrl)
            }

            // Add channel image URLs
            if let channelImageUrl = live.channel?.imageUrl {
                imageUrls.insert(channelImageUrl)
            }
        }

        // Clear cache and re-download images
        await withTaskGroup(of: Void.self) { group in
            for imageUrl in imageUrls {
                group.addTask {
                    // Remove from cache first to force fresh download
                    await ImageCacheManager.shared.removeImage(for: imageUrl)
                    // Then download fresh image
                    _ = await ImageCacheManager.shared.downloadAndCacheImage(from: imageUrl)
                }
            }
        }

        logger.debug("Force refreshed \(imageUrls.count) images for FollowingLives")
    }
}
