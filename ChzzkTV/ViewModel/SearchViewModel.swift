import Foundation

@MainActor
class SearchViewModel: ObservableObject {
    @Published var channels: [UIChannel] = []
    @Published var suggestions: [String] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private let searchService: SearchServiceProtocol
    
    init(searchService: SearchServiceProtocol) {
        self.searchService = searchService
    }
        
    func search(query: String) async {
        guard !query.isEmpty else { return }
        
        isLoading = true
        error = nil
        
        async let channelTask: () = searchChannels(query: query)
        async let suggestionTask: () = searchSuggestions(query: query)
        
        await _ = [channelTask, suggestionTask]
        
        isLoading = false
    }
    
    private func searchChannels(query: String) async {
        do {
            channels = try await searchService.getChannels(keyword: query)
        } catch {
            self.error = error
        }
    }
    
    private func searchSuggestions(query: String) async {
        do {
            suggestions = try await searchService.getSuggestions(keyword: query)
        } catch {
            self.error = error
        }
    }
}
