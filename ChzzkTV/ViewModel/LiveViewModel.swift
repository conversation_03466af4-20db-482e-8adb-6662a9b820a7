import Foundation
import SwiftData

enum LiveSortType: String {
    case popular = "POPULAR"
    case latest = "LATEST"
}

@MainActor
class LiveViewModel: ObservableObject {
    @Published var streams: [UILiveStream] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private let liveService: LiveServiceProtocol
    private var hasNextPage: Bool = true
    private var isLoadingMore = false
    private var sortOption = LiveSortType.popular
    
    init(
        liveService: LiveServiceProtocol,
        sortOption: LiveSortType = .popular
    ) {
        self.liveService = liveService
        self.sortOption = sortOption
    }
    
    func loadStreams(reset: Bool = false) async {
        guard !isLoading else { return }
        isLoading = true
        error = nil
        
        if reset {
            streams = []
            hasNextPage = false
        }
        
        do {
            let (streams, hasNextPage) = try await liveService.getAllLives(type: sortOption)
            self.streams = streams
            self.hasNextPage = hasNextPage
        } catch {
            self.error = error
        }
        
        isLoading = false
    }
    
    func loadMoreStreamsIfNeeded() async {
        guard
            !isLoading,
            !isLoadingMore,
            hasNextPage else { return }
        
        isLoadingMore = true
        
        do {
            let (streams, hasNextPage) = try await liveService.getNextAllLives(type: sortOption)

            // Filter out streams that already exist in the current list
            let uniqueNewStreams = streams.filter { newStream in
                !self.streams.contains { existingStream in
                    existingStream.id == newStream.id
                }
            }
            
            self.streams.append(contentsOf: uniqueNewStreams)
            self.hasNextPage = hasNextPage
        } catch {
            self.error = error
        }
        
        isLoadingMore = false
    }
}
