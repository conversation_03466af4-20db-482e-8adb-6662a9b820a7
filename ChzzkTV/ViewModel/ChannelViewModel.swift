import Foundation
import SwiftData
import SwiftUI

@MainActor
class ChannelViewModel: ObservableObject {
    @Published var channel: UIChannel?
    @Published var liveStream: UILiveStream?
    @Published var videos: [UIVideo] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    // Pagination
    @Published var currentPage = 0
    @Published var hasMoreVideos = true
    @Published var isLoadingVideos = false
    private let pageSize = 16
    
    private let channelService: ChannelServiceProtocol
    
    init(
        channelService: ChannelServiceProtocol
    ) {
        self.channelService = channelService
    }
    
    func loadChannel(channelId: String) async {
        // Only set isLoading if videos aren't already loading
        if !isLoadingVideos {
            isLoading = true
        }
        error = nil
        
        do {
            channel = try await channelService.getChannel(id: channelId)
        } catch {
            self.error = error
        }
        
        // Only clear isLoading if videos aren't loading
        if !isLoadingVideos {
            isLoading = false
        }
    }
    
    func loadChannelData(channelId: String) async {
        // Only set isLoading if videos aren't already loading
        if !isLoadingVideos {
            isLoading = true
        }
        error = nil
        
        do {
            if let live = try await channelService.getLive(id: channelId) {
                self.liveStream = live
            }
        } catch {
            self.error = error
        }
        
        // Only clear isLoading if videos aren't loading
        if !isLoadingVideos {
            isLoading = false
        }
    }
    
    func loadChannelVideos(channelId: String, usePagination: Bool = false) async {
        // If not using pagination, reset to first page
        if !usePagination {
            currentPage = 0
            videos = []
            hasMoreVideos = true
        }
        
        // Don't load more if we're already loading videos or if there are no more videos
        guard !isLoadingVideos && hasMoreVideos else { return }
        
        isLoadingVideos = true
        // We'll keep using isLoading for UI states, but it won't block video loading
        if !usePagination {
            isLoading = true
        }
        error = nil
        
        do {
            let (fetchedVideo, totalPages) = try await channelService.getVideos(id: channelId, page: currentPage, size: pageSize)
            
            if let fetchedVideo = fetchedVideo, let totalPages = totalPages {
                if !usePagination {
                    videos = fetchedVideo
                } else {
                    videos.append(contentsOf: fetchedVideo)
                }
                
                // Update pagination info
                currentPage += 1
                hasMoreVideos = currentPage < totalPages
            } else {
                hasMoreVideos = false
            }
        } catch {
            self.error = error
        }
        
        isLoadingVideos = false
        if !usePagination {
            isLoading = false
        }
    }
    
    func loadAll(channelId: String) async {
        // Set common loading state
        isLoading = true
        
        // Load channel and channel data concurrently
        async let channelTask: () = loadChannel(channelId: channelId)
        async let channelDataTask: () = loadChannelData(channelId: channelId)
        
        // Wait for channel and channel data to load
        _ = await [channelTask, channelDataTask]
        
        // Load videos separately to avoid passing ModelContext in async let
        await loadChannelVideos(channelId: channelId, usePagination: false)
        
        liveStream?.channel = channel
    }
    
    func isFollowing(channelId: String) -> Bool {
        channelService.isFollowing(channelId: channelId)
    }
    
    func toggleFollow() async -> Bool? {
        guard let channel = channel else { return nil }
        return await channelService.toggleFollow(channelId: channel.id)
    }
}
