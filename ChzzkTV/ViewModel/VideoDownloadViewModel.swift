//
//  VideoDownloadViewModel.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import Foundation
import SwiftUI
import os.log

@MainActor
class VideoDownloadViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var downloadService: VideoDownloadService
    @Published var showingDownloadOptions = false
    @Published var selectedVideo: UIVideo?
    @Published var availableQualities: [VideoQuality] = []
    @Published var selectedQuality: VideoQuality?
    @Published var isLoadingQualities = false
    @Published var downloadError: Error?
    @Published var showingDownloadError = false
    
    // MARK: - Private Properties
    private let logger = Logger(subsystem: "ChzzkTV", category: "VideoDownloadViewModel")
    private let videoService: VideoServiceProtocol
    
    // MARK: - Initialization
    init(videoService: VideoServiceProtocol = VideoService()) {
        self.videoService = videoService
        self.downloadService = VideoDownloadService.shared
    }
    
    // MARK: - Public Methods
    
    /// Initiates the download process for a video
    func startDownload(for video: UIVideo) {
        selectedVideo = video
        loadAvailableQualities(for: video)
        showingDownloadOptions = true
    }
    
    /// Downloads the video with the selected quality
    func downloadVideo() async {
        guard let video = selectedVideo,
              let quality = selectedQuality else {
            logger.error("Missing video or quality for download")
            return
        }
        
        do {
            logger.info("Starting download for video: \(video.title) at \(quality.displayName)")
            _ = try await downloadService.downloadVideo(video, quality: quality)
            
            // Reset UI state
            await MainActor.run {
                showingDownloadOptions = false
                selectedVideo = nil
                selectedQuality = nil
                availableQualities = []
            }
            
        } catch {
            logger.error("Download failed: \(error.localizedDescription)")
            await MainActor.run {
                // Provide user-friendly error messages
                let nsError = error as NSError
                if nsError.code == -12174 {
                    downloadError = NSError(
                        domain: "VideoDownload",
                        code: -12174,
                        userInfo: [NSLocalizedDescriptionKey: "This video is protected and cannot be downloaded. Only unprotected content can be saved for offline viewing."]
                    )
                } else {
                    downloadError = error
                }
                showingDownloadError = true
            }
        }
    }
    
    /// Cancels an active download
    func cancelDownload(for video: UIVideo) {
        downloadService.cancelDownload(for: video.id)
        logger.info("Cancelled download for video: \(video.title)")
    }
    
    /// Deletes a downloaded video
    func deleteDownload(for video: UIVideo) {
        do {
            try downloadService.deleteDownloadedVideo(for: video.id)
            logger.info("Deleted download for video: \(video.title)")
        } catch {
            logger.error("Failed to delete download: \(error.localizedDescription)")
            downloadError = error
            showingDownloadError = true
        }
    }
    
    /// Gets the download state for a video
    func getDownloadState(for video: UIVideo) -> VideoDownloadState? {
        return downloadService.getDownloadProgress(for: video.id)
    }
    
    /// Checks if a video is downloaded
    func isVideoDownloaded(_ video: UIVideo) -> Bool {
        if case .completed = getDownloadState(for: video) {
            return true
        }
        return false
    }
    
    /// Checks if a video is currently downloading
    func isVideoDownloading(_ video: UIVideo) -> Bool {
        if case .downloading = getDownloadState(for: video) {
            return true
        }
        return false
    }
    
    /// Gets the download progress for a video (0.0 to 1.0)
    func getDownloadProgress(for video: UIVideo) -> Double {
        if case .downloading(let progress) = getDownloadState(for: video) {
            return progress
        }
        return 0.0
    }
    
    /// Gets all downloaded videos
    func getDownloadedVideos() -> [VideoDownloadInfo] {
        return downloadService.getDownloadedVideos()
    }
    
    /// Dismisses the download options sheet
    func dismissDownloadOptions() {
        showingDownloadOptions = false
        selectedVideo = nil
        selectedQuality = nil
        availableQualities = []
    }
    
    /// Dismisses the error alert
    func dismissError() {
        showingDownloadError = false
        downloadError = nil
    }
    
    // MARK: - Private Methods
    
    private func loadAvailableQualities(for video: UIVideo) {
        isLoadingQualities = true
        
        Task {
            do {
                let qualities = try await videoService.getAvailableQualities(for: video.id)
                
                await MainActor.run {
                    self.availableQualities = qualities
                    self.selectedQuality = qualities.first // Default to highest quality
                    self.isLoadingQualities = false
                }
                
            } catch {
                logger.error("Failed to load qualities: \(error.localizedDescription)")
                
                await MainActor.run {
                    self.downloadError = error
                    self.showingDownloadError = true
                    self.isLoadingQualities = false
                }
            }
        }
    }
}

// MARK: - Helper Extensions

extension VideoDownloadState {
    var displayText: String {
        switch self {
        case .pending:
            return "Pending"
        case .downloading(let progress):
            return "Downloading \(Int(progress * 100))%"
        case .processing:
            return "Processing"
        case .completed:
            return "Downloaded"
        case .failed(let errorMessage):
            return "Failed: \(errorMessage)"
        case .cancelled:
            return "Cancelled"
        }
    }
    
    var isActive: Bool {
        switch self {
        case .pending, .downloading, .processing:
            return true
        case .completed, .failed, .cancelled:
            return false
        }
    }
}

extension VideoDownloadInfo {
    var formattedFileSize: String {
        if totalBytes > 0 {
            return VideoDownloadURLExtractor.formatFileSize(totalBytes)
        }
        return "Unknown size"
    }
    
    var formattedDuration: String {
        let elapsed = Date().timeIntervalSince(startTime)
        return VideoDownloadURLExtractor.formatTimeInterval(elapsed)
    }
}
