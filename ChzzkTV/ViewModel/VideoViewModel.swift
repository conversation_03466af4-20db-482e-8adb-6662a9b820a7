//
//  VideoViewModel.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/25/25.
//

import AVKit
import MediaPlayer
import SwiftData

enum VideoPlaybackError: Error {
    case missingVideoData(String)
    case invalidM3U8URL
    case invalidPlaylist
    case serverError
}

enum VideoDetailError: Error {
    case videoIdNotFound
    case unknownPlaybackResponse
    case invalidUrl
    case networkError(String)
    case parserError(String)
    case invalidGdaParameter
}

@MainActor
class VideoViewModel: ObservableObject {
    // MARK: - Properties
    @Published private(set) var player: AVPlayer?
    @Published var isLoading = false
    @Published var error: Error?
    @Published var video: UIVideo?
    @Published private(set) var availableQualities: [VideoQuality] = []
    @Published private(set) var currentQuality: VideoQuality?
    
    private var playerItem: AVPlayerItem?
    private var timeObserver: Any?
    private var videoService: VideoServiceProtocol
    
    init(videoService: VideoServiceProtocol) {
        self.videoService = videoService
    }
    
    // MARK: - Public Methods
    func loadVideo(_ videoId: Int) async {
        isLoading = true
        error = nil
        
        do {
            video = try await videoService.getVideo(videoId)
            availableQualities = try await videoService.getAvailableQualities(for: videoId)
            currentQuality = availableQualities.first
            
            if let quality = currentQuality {
                let asset = try await videoService.createAsset(for: videoId, quality: quality)
                let imageData = await video?.channel?.imageData()
                setupPlayer(with: asset, imageData: imageData)
            }
        } catch {
            self.error = error
            print("error: \(error)")
        }
        
        isLoading = false
    }
    
    func changeQuality(_ quality: VideoQuality) async {
        guard let videoId = video?.id else { return }
        
        isLoading = true
        error = nil
        
        do {
            // Store current playback position
            let currentTime = player?.currentTime()
            
            // Clean up existing player
            cleanUp()
            
            let asset = try await videoService.createAsset(for: videoId, quality: quality)
            let imageData = await video?.channel?.imageData()
            
            // Setup new player with selected quality
            setupPlayer(with: asset, imageData: imageData)
            
            // Restore playback position
            if let time = currentTime {
                await player?.seek(to: time)
            }
            
            currentQuality = quality
        } catch {
            self.error = error
            print("error: \(error)")
        }
        
        isLoading = false
    }
    
    func prepareForDismissal() {
        cleanUp()
    }
    
    func resetError() {
        error = nil
    }
    
    func togglePlayback() {
        guard let player = player else { return }
        
        if player.rate > 0 {
            player.pause()
        } else {
            player.play()
        }
        updateNowPlayingInfo()
    }
    
    // MARK: - Private Methods
    private func setupPlayer(with asset: AVAsset, imageData: Data?) {
        self.playerItem = AVPlayerItem(asset: asset)
        self.playerItem?.externalMetadata = createMetadataItems(for: video, imageData: imageData)
        self.player = AVPlayer(playerItem: playerItem)
        self.player?.audiovisualBackgroundPlaybackPolicy = .continuesIfPossible

        // Restore last playback position if available
        if let progress = video?.progress,
           let duration = video?.duration {
            let lastPosition = progress * Double(duration)
            let time = CMTime(seconds: lastPosition, preferredTimescale: 1)
            player?.seek(to: time)
        }
        
        // Add periodic time observer
        let interval = CMTime(seconds: 10.0, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        timeObserver = player?.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            Task { @MainActor in
                if let video = self?.video {
                    try await self?.videoService.watchEvent(
                        time: Int(time.seconds),
                        video: video,
                        event: .continued
                    )
                }
            }
        }
        
        self.player?.play()
    }
    
    private func createMetadataItems(
        for video: UIVideo?,
        imageData: Data?
    ) -> [AVMetadataItem] {
        var mapping: [AVMetadataIdentifier: Any] = [
            .commonIdentifierTitle: video?.title ?? "Unknown",
            .iTunesMetadataTrackSubTitle: video?.channel?.name ?? "",
            .commonIdentifierDescription:
"""
\(video?.category ?? "")
\(video?.readableDate ?? "")
\(video?.channel?.followerCountFormatted ?? "")
"""
        ]
        if let imageData = imageData {
            mapping[.commonIdentifierArtwork] = imageData
        }
        
        return mapping.compactMap { createMetadataItem(for: $0, value: $1) }
    }
    
    private func createMetadataItem(for identifier: AVMetadataIdentifier,
                                    value: Any) -> AVMetadataItem {
        let item = AVMutableMetadataItem()
        item.identifier = identifier
        item.value = value as? NSCopying & NSObjectProtocol
        // Specify "und" to indicate an undefined language.
        item.extendedLanguageTag = "und"
        return item.copy() as! AVMetadataItem
    }
    
    private func cleanUp() {
        if var video = self.video,
           let player = self.player {
            let seconds = CMTimeGetSeconds(player.currentTime())
            let time = Int(floor(seconds))
            video.progress = Double(time / video.duration)
            Task {
                try await videoService.watchEvent(
                    time: time,
                    video: video,
                    event: .ended
                )
            }
        }
        
        // Stop and reset player
        player?.pause()
        player?.replaceCurrentItem(with: nil)
        player = nil
        
        // Reset audio session
        try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
        
        // Clear player item
        playerItem = nil
    }
    
    private func deactivateAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            print("Failed to deactivate audio session: \(error)")
        }
    }
    
    private func updateNowPlayingInfo() {
        var nowPlayingInfo = [String: Any]()
        
        nowPlayingInfo[MPMediaItemPropertyTitle] = video?.title ?? "Unknown"
        nowPlayingInfo[MPMediaItemPropertyArtist] = video?.channel?.name ?? ""
        
        if let duration = video?.duration {
            nowPlayingInfo[MPMediaItemPropertyPlaybackDuration] = Double(duration)
        }
        
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = player?.rate ?? 1.0
        
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
}
