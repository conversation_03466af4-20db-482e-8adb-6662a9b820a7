import Foundation
import SwiftUI

@MainActor
class CategoryViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var categories: [UICategory] = []
    @Published var state: LoadingState = .idle
    @Published var isLoadingMore: Bool = false
    private var hasNextPage: Bool = true

    // MARK: - Caching Properties
    private var cachedCategories: [UICategory] = []
    private var lastLoadTime: Date?
    private let refreshInterval: TimeInterval = 300 // 5 minutes

    // MARK: - Dependencies
    private let categoryService: CategoryServiceProtocol
    
    // MARK: - State Enum
    enum LoadingState {
        case idle
        case loading
        case loaded
        case error(Error)
        
        var isLoading: Bool {
            if case .loading = self { return true }
            return false
        }
        
        var error: Error? {
            if case .error(let error) = self { return error }
            return nil
        }
    }
    
    // MARK: - Initialization
    init(categoryService: CategoryServiceProtocol) {
        self.categoryService = categoryService
    }
    
    // MARK: - Public Methods
    func loadCategories(forceRefresh: Bool = false) async {
        // Prevent concurrent loading
        if case .loading = state { return }

        // Check if we should skip refresh based on time interval
        if !forceRefresh, let lastLoad = lastLoadTime,
           Date().timeIntervalSince(lastLoad) < refreshInterval {
            return
        }

        // Always show cached data first if available
        let hasCachedData = !cachedCategories.isEmpty
        if hasCachedData {
            categories = cachedCategories
            if case .idle = state {
                state = .loaded
            }
        }

        // Set loading state only if we don't have cached data
        if !hasCachedData {
            state = .loading
        }

        do {
            let (newCategories, hasNext) = try await categoryService.getCategories(usePagination: false)

            // Update cached data
            cachedCategories = newCategories
            lastLoadTime = Date()

            // Update published properties
            self.categories = newCategories
            self.hasNextPage = hasNext

            state = .loaded
        } catch {
            // Only show error if we don't have cached data to fall back on
            if !hasCachedData {
                state = .error(error)
            }
            // If we have cached data, silently fail and keep showing cached data
        }
    }
    
    func loadMoreCategories() async {
        guard !state.isLoading && !isLoadingMore && hasNextPage else { return }
        
        isLoadingMore = true
        
        do {
            let (newCategories, hasNext) = try await categoryService.getCategories(usePagination: true)
            
            // Filter out categories that already exist in the current list
            let uniqueNewCategories = newCategories.filter { newCategory in
                !self.categories.contains { existingCategory in
                    existingCategory.id == newCategory.id
                }
            }
            
            self.categories.append(contentsOf: uniqueNewCategories)
            self.hasNextPage = hasNext
            
            isLoadingMore = false
        } catch {
            isLoadingMore = false
            // We don't set state to error here to preserve the currently loaded content
            // Could optionally show a toast or other non-blocking error indicator
        }
    }
    
    func retry() async {
        await loadCategories()
    }
}
