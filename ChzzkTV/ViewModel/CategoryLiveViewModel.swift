//
//  CategoryLiveViewModel.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/5/25.
//

import SwiftUI
import SwiftData

@MainActor
class CategoryLiveViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var streams: [UILiveStream] = []
    @Published var state: LoadingState = .idle
    @Published var isLoadingMore: Bool = false
    private var hasNextPage: Bool = true

    // MARK: - Caching Properties
    private var cachedStreams: [UILiveStream] = []
    private var lastLoadTime: Date?
    private let refreshInterval: TimeInterval = 60 // 1 minute for live streams

    // MARK: - Dependencies
    private let categoryService: CategoryServiceProtocol
    private let category: UICategory
    
    // MARK: - State Enum
    enum LoadingState {
        case idle
        case loading
        case loaded
        case error(Error)
        
        var isLoading: Bool {
            if case .loading = self { return true }
            return false
        }
        
        var error: Error? {
            if case .error(let error) = self { return error }
            return nil
        }
    }
    
    // MARK: - Initialization
    init(categoryService: CategoryServiceProtocol,
         category: UICategory) {
        self.categoryService = categoryService
        self.category = category
    }
    
    // MARK: - Public Methods
    func loadStreams(forceRefresh: Bool = false) async {
        // Prevent concurrent loading
        if case .loading = state { return }

        // Check if we should skip refresh based on time interval
        if !forceRefresh, let lastLoad = lastLoadTime,
           Date().timeIntervalSince(lastLoad) < refreshInterval {
            return
        }

        // Always show cached data first if available
        let hasCachedData = !cachedStreams.isEmpty
        if hasCachedData {
            streams = cachedStreams
            if case .idle = state {
                state = .loaded
            }
        }

        // Set loading state only if we don't have cached data
        if !hasCachedData {
            state = .loading
        }

        do {
            let (newStreams, hasNext) = try await categoryService.getLivesInCategory(category, usePagination: false)

            // Update cached data
            cachedStreams = newStreams
            lastLoadTime = Date()

            // Update published properties
            self.streams = newStreams
            self.hasNextPage = hasNext

            state = .loaded
        } catch {
            // Only show error if we don't have cached data to fall back on
            if !hasCachedData {
                state = .error(error)
            }
            // If we have cached data, silently fail and keep showing cached data
        }
    }
    
    func loadMoreStreamsIfNeeded() async {
        guard !state.isLoading && !isLoadingMore && hasNextPage else { return }
        
        isLoadingMore = true
        
        do {
            let (newStreams, hasNext) = try await categoryService.getLivesInCategory(category, usePagination: true)
            
            // Filter out streams that already exist in the current list
            let uniqueNewStreams = newStreams.filter { newStream in
                !self.streams.contains { existingStream in
                    existingStream.id == newStream.id
                }
            }
            
            self.streams.append(contentsOf: uniqueNewStreams)
            self.hasNextPage = hasNext
            
            isLoadingMore = false
        } catch {
            isLoadingMore = false
            // We don't set state to error here to preserve the currently loaded content
        }
    }
    
    func retry() async {
        await loadStreams()
    }
}
