//
//  ChzzkTVApp.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/15/25.
//

import SwiftUI
import SwiftData

@main
struct ChzzkTVApp: App {
    @Environment(\.scenePhase) private var scenePhase
    @StateObject private var loginViewModel = QRCodeLoginViewModel()
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    // Initialize the download service to ensure it loads completed downloads
    private let downloadService: VideoDownloadService = {
        print("🔥 APP: Initializing VideoDownloadService.shared...")
        return VideoDownloadService.shared
    }()
    
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(loginViewModel)
                .tint(.chzzk)
        }
        .modelContainer(sharedModelContainer)
    }
}
