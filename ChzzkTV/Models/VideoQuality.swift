import Foundation

struct VideoQuality: Identifiable, Equatable, Codable {
    let id: String
    let resolution: Int
    let bandwidth: Int
    let url: URL
    
    var displayName: String {
        if resolution >= 1080 {
            return "1080p"
        } else if resolution >= 720 {
            return "720p"
        } else if resolution >= 480 {
            return "480p"
        } else if resolution >= 360 {
            return "360p"
        } else {
            return "\(resolution)p"
        }
    }
    
    static func == (lhs: VideoQuality, rhs: VideoQuality) -> Bool {
        lhs.id == rhs.id
    }
} 