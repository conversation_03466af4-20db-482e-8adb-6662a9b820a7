//
//  LiveStreamDetail.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/16/25.
//

import Foundation

struct LivePlayback: Decodable {
    let meta: Meta
    let serviceMeta: ServiceMeta
    let live: LivePlaybackInfo
    let api: [API]
    let media: [Media]
    let thumbnail: ThumbnailInfo
    let multiview: [String] // empty array as per TypeScript interface
}

struct Meta: Decodable {
    let videoId: String
    let streamSeq: Int
    let liveId: String
    let paidLive: Bool
    let cdnInfo: CdnInfo?
    let p2p: Bool?
}

struct ServiceMeta: Decodable {
    let contentType: String
}

struct CdnInfo: Decodable {
    let cdnType: String?
    let zeroRating: Bool?
}

struct LivePlaybackInfo: Decodable {
    let start: String
    let open: String
    let timeMachine: Bool
    let status: String
}

struct API: Decodable {
    let name: String
    let path: String
}

struct Media: Decodable {
    let mediaId: String
    let `protocol`: String
    let path: String
    let encodingTrack: [EncodingTrack]
}

struct ThumbnailInfo: Decodable {
    let snapshotThumbnailTemplate: String
    let types: [String]
}

struct EncodingTrack: Decodable {
    let encodingTrackId: String
    let audioBitRate: Int
    let audioSamplingRate: Int
    let audioChannel: Int
    let avoidReencoding: Bool
    let audioOnly: Bool?
    
    // Video-specific properties (optional)
    let videoProfile: String?
    let audioProfile: String?
    let videoCodec: String?
    let videoBitRate: Int?
    let videoFrameRate: String?
    let videoWidth: Int?
    let videoHeight: Int?
    let videoDynamicRange: String?
    
    // Audio-specific properties (optional)
    let audioCodec: String?
    let path: String?
}

class LiveStreamDetail: LiveStream {
    let status: String
    let closeDate: String?
    let clipActive: Bool
    let chatActive: Bool
    let chatAvailableGroup: String
    let paidPromotion: Bool
    let chatAvailableCondition: String
    let minFollowerMinute: Int
    let p2pQuality: [String]?
    let userAdultStatus: String?
    let chatDonationRankingExposure: Bool
    let adParameter: AdParameter
    let dropsCampaignNo: Int?
    let krOnlyViewing: Bool
    let livePlaybackJson: String?
    let livePollingStatusJson: String
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        status = try container.decode(String.self, forKey: .status)
        closeDate = try container.decodeIfPresent(String.self, forKey: .closeDate)
        clipActive = try container.decode(Bool.self, forKey: .clipActive)
        chatActive = try container.decode(Bool.self, forKey: .chatActive)
        chatAvailableGroup = try container.decode(String.self, forKey: .chatAvailableGroup)
        paidPromotion = try container.decode(Bool.self, forKey: .paidPromotion)
        chatAvailableCondition = try container.decode(String.self, forKey: .chatAvailableCondition)
        minFollowerMinute = try container.decode(Int.self, forKey: .minFollowerMinute)
        p2pQuality = try container.decodeIfPresent([String].self, forKey: .p2pQuality)
        userAdultStatus = try container.decodeIfPresent(String.self, forKey: .userAdultStatus)
        chatDonationRankingExposure = try container.decode(Bool.self, forKey: .chatDonationRankingExposure)
        adParameter = try container.decode(AdParameter.self, forKey: .adParameter)
        dropsCampaignNo = try container.decodeIfPresent(Int.self, forKey: .dropsCampaignNo)
        krOnlyViewing = try container.decode(Bool.self, forKey: .krOnlyViewing)
        livePlaybackJson = try container.decodeIfPresent(String.self, forKey: .livePlaybackJson)
        livePollingStatusJson = try container.decode(String.self, forKey: .livePollingStatusJson)
        
        try super.init(from: decoder)
    }
    
    private enum CodingKeys: String, CodingKey {
        case status, closeDate, clipActive, chatActive, chatAvailableGroup, paidPromotion, chatAvailableCondition, minFollowerMinute, p2pQuality, userAdultStatus, chatDonationRankingExposure, adParameter, dropsCampaignNo, krOnlyViewing, livePlaybackJson, livePollingStatusJson
    }
}

extension LiveStreamDetail {
    func livePlayback() throws -> LivePlayback {
        guard let data = livePlaybackJson?.data(using: .utf8) else {
            throw NSError(domain: "LiveStreamDetail", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert livePlaybackJson to data"])
        }
        return try JSONDecoder().decode(LivePlayback.self, from: data)
    }
    
    func livePollingStatus() throws -> LivePollingStatus {
        guard let data = livePollingStatusJson.data(using: .utf8) else {
            throw NSError(domain: "LiveStreamDetail", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert livePollingStatusJson to data"])
        }
        return try JSONDecoder().decode(LivePollingStatus.self, from: data)
    }
    
    func toUILiveStream(qualities: [VideoQuality]? = nil) -> UILiveStream {
        var stream = super.toUILiveStream()
        stream.qualities = qualities
        return stream
    }
}

struct AdParameter: Decodable {
    let tag: String
}

struct LivePollingStatus: Decodable {
    let status: String
    let isPublishing: Bool
    let playableStatus: String
    let trafficThrottling: Int
    let callPeriodMilliSecond: Int
}

struct LiveStreamDetailResponse: Decodable {
    let code: Int
    let message: String?
    let content: LiveStreamDetail
}

struct LiveStatus: Decodable {
    let liveTitle: String
    let status: String // "OPEN" or "CLOSE"
    let concurrentUserCount: Int
    let accumulateCount: Int
    let paidPromotion: Bool
    let adult: Bool
    let krOnlyViewing: Bool
    let chatChannelId: String
    let tags: [String]
    let categoryType: String
    let liveCategory: String?
    let liveCategoryValue: String?
    let livePollingStatusJson: String
    let faultStatus: String?
    let userAdultStatus: String?
    let blindType: String?
    let chatActive: Bool
    let chatAvailableGroup: String
    let chatAvailableCondition: String
    let minFollowerMinute: Int
    let chatDonationRankingExposure: Bool
    let dropsCampaignNo: Int?
    let liveTokenList: [String]
    
    func livePollingStatus() throws -> LivePollingStatus {
        guard let data = livePollingStatusJson.data(using: .utf8) else {
            throw NSError(domain: "LiveStreamDetail", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert livePollingStatusJson to data"])
        }
        return try JSONDecoder().decode(LivePollingStatus.self, from: data)
    }
}

struct LiveStatusResponse: Decodable {
    let code: Int
    let message: String?
    let content: LiveStatus
}
