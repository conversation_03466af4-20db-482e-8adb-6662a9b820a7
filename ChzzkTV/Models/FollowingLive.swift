//
//  FollowingList.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/6/25.
//

import Foundation

struct FollowingLiveResponse: Decodable {
    let code: Int
    let message: String?
    let content: FollowingLiveContent
}

struct FollowingLiveContent: Decodable {
    let totalCount: Int
    let followingList: [FollowingLive]
}

struct FollowingLive: Decodable {
    let channel: Channel
    let liveInfo: LiveStream
}
