//
//  Search.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/27/25.
//

import Foundation

struct SearchResponse: Decodable {
    let code: Int
    let message: String?
    let content: SearchContent?
}

struct SearchContent: Decodable {
    let size: Int
    let page: Page?
    let data: [SearchResult]
}

struct SearchResult: Decodable {
    let channel: Channel
    let content: SearchResultContent?
}

struct SearchResultContent: Decodable {
    let live: LiveStreamDetail?
    let videos: [Video]
}

struct SearchAutocompleResponse: Decodable {
    let code: Int
    let message: String?
    let content: SearchAutocompleteContent
}

struct SearchAutocompleteContent: Decodable {
    let page: Int
    let size: Int
    let totalCount: Int
    let totalPages: Int
    let data: [String]
}
