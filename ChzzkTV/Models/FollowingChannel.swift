//
//  FollowingChannel.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/6/25.
//

import Foundation

struct FollowingChannel: Decodable {
    let channel: Channel
}

struct FollowingChannelContent: Decodable {
    let totalCount: Int
    let totalPage: Int
    let followingList: [FollowingChannel]
}

struct FollowingChannelResponse: Decodable {
    let code: Int
    let message: String?
    let content: FollowingChannelContent
}
