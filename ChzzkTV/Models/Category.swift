//
//  Category.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/4/25.
//

import Foundation

struct CategoryResponse: Decodable {
    let code: Int
    let message: String?
    let content: CategoryContent
}

struct CategoryContent: Decodable {
    let size: Int
    let page: Page?
    let data: [Category]
}

struct Category: Decodable {
    let categoryType: String
    let categoryId: String
    let categoryValue: String
    let posterImageUrl: String?
    let openLiveCount: Int
    let concurrentUserCount: Int
    let newCategory: Bool
}

extension Category {
    var imageUrl: URL? {
        guard let urlString = posterImageUrl else { return nil }
        return URL(string: urlString)
    }
    
    func toUICategory() -> UICategory {
        UICategory(
            id: self.categoryId,
            type: self.categoryType,
            value: self.categoryValue,
            imageUrl: imageUrl,
            channelCount: openLiveCount,
            viewerCount: concurrentUserCount,
            isNew: self.newCategory
        )
    }
}
