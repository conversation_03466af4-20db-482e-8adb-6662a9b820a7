import Foundation

class LiveStream: Decodable {
    let liveId: Int
    let liveTitle: String
    let liveImageUrl: String?
    let concurrentUserCount: Int
    let openDate: String?
    let adult: Bool
    let categoryType: String?
    let liveCategory: String?
    let liveCategoryValue: String?
    let tags: [String]
    let watchPartyTag: String?
    var channel: Channel?
    let blindType: String?
}

extension LiveStream {
    func imageUrl(size: Int = 720) -> URL? {
        guard let urlString = liveImageUrl else { return nil }
        return URL(string: urlString.replacingOccurrences(of: "{type}", with: "\(size)"))
    }
    
    var trimmedTitle: String {
        liveTitle
            .trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
            .replacingOccurrences(of: "\n", with: " ")
    }
    
    var timestamp: TimeInterval {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.timeZone = TimeZone(identifier: "Asia/Seoul")
        
        if let openDate = openDate, let date = dateFormatter.date(from: openDate) {
            return date.timeIntervalSince1970
        } else {
            return Date.now.timeIntervalSince1970
        }
    }
    
    func toUILiveStream(size: Int = 720) -> UILiveStream {
        UILiveStream(
            id: liveId,
            title: trimmedTitle,
            imageUrl: imageUrl(size: size),
            viewerCount: concurrentUserCount,
            timestamp: timestamp,
            category: liveCategoryValue,
            channel: channel?.toUIChannel(),
            isAbroad: blindType == "ABROAD",
            isAdult: adult
        )
    }
}

struct LiveStreamResponse: Decodable {
    let code: Int
    let message: String?
    let content: LiveStreamContent
}

struct Next: Decodable {
    let concurrentUserCount: Int?
    
    let liveId: Int? // lives
    
    let offset: Int? // search channel
    
    let openLiveCount: Int? // category
    let categoryId: String? // category
    
    let nextNo: String? // following video
}

struct Page: Decodable {
    let next: Next?
}

struct LiveStreamContent: Decodable {
    let data: [LiveStream]
    let page: Page?
    let size: Int
}
