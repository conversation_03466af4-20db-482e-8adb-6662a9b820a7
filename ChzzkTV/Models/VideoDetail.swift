//
//  VideoDetail.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/22/25.
//

import Foundation

class VideoDetail: Video {
    let inKey: String?
    let vodStatus: String?
    let nextVideo: Video?
    let liveRewindPlaybackJson: String?
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        inKey = try container.decodeIfPresent(String.self, forKey: .inKey)
        vodStatus = try container.decode(String.self, forKey: .vodStatus)
        nextVideo = try container.decodeIfPresent(Video.self, forKey: .nextVideo)
        liveRewindPlaybackJson = try container.decodeIfPresent(String.self, forKey: .liveRewindPlaybackJson)
        try super.init(from: decoder)
    }
    
    private enum CodingKeys: String, CodingKey {
        case inKey, vodStatus, nextVideo, liveRewindPlaybackJson
    }
}

extension VideoDetail {
    var liveRewindPlayback: LiveRewindPlayback? {
        guard let data = liveRewindPlaybackJson?.data(using: .utf8) else {
            return nil
        }
        
        do {
            return try JSONDecoder().decode(LiveRewindPlayback.self, from: data)
        } catch {
            print("Failed to decode: \(error.localizedDescription)")
            return nil
        }
    }
}

struct VideoDetailResponse: Decodable {
    let code: Int
    let message: String?
    let content: VideoDetail
}

struct LiveRewindPlayback: Decodable {
    let meta: Meta
    let api: [API]
    let media: [Media]
}
