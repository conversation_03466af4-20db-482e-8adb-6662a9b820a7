//
//  FollowingVideo.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/6/25.
//

import Foundation

struct FollowingVideo: Decodable {
    let video: Video
    let channel: Channel
}

struct FollowingVideoContent: Decodable {
    let size: Int
    let page: Page?
    let data: [Video]
}

struct FollowingVideoResponse: Decodable {
    let code: Int
    let message: String?
    let content: FollowingVideoContent
}
