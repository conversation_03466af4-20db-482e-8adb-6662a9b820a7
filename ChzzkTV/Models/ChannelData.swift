//
//  ChannelData.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/21/25.
//

import Foundation

struct TopExposedVideos: Decodable {
    let openLive: LiveStream?
    let latestVideo: Video?
}

struct ChannelDataContent: Decodable {
    let topExposedVideos: TopExposedVideos?
}

struct ChannelDataResponse: Decodable {
    let code: Int
    let message: String?
    let content: ChannelDataContent?
}
