//
//  Video.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/21/25.
//

import Foundation

class Video: Decodable {
    let videoNo: Int
    let videoId: String?
    let videoTitle: String
    let videoType: String
    let publishDate: String
    let thumbnailImageUrl: String?
    let duration: Int
    let publishDateAt: TimeInterval
    let categoryType: String?
    let videoCategory: String?
    let videoCategoryValue: String?
    let adult: Bool
    let clipActive: Bool?
    let blindType: String?
    let watchTimeline: Int?
    let channel: Channel?
    
    func toUIVideo() -> UIVideo {
        var imageUrl: URL?
        if let thumbnailImageUrl = thumbnailImageUrl {
            imageUrl = URL(string: thumbnailImageUrl)
        }
        
        var progress: Double?
        if let watchTimeline = watchTimeline {
            progress = Double(watchTimeline) / Double(duration)
        }
        
        return UIVideo(
            id: videoNo,
            uid: videoId,
            title: trimmedTitle,
            imageUrl: imageUrl,
            duration: duration,
            timestamp: publishDateAt / 1000,
            category: videoCategoryValue,
            channel: channel?.toUIChannel(),
            progress: progress,
            isAbroad: blindType == "ABROAD",
            isAdult: adult
        )
    }
}

extension Video {
    var trimmedTitle: String {
        videoTitle
            .trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
            .replacingOccurrences(of: "\n", with: " ")
    }
}

struct VideoListContent: Decodable {
    let page: Int
    let size: Int
    let totalCount: Int
    let totalPages: Int
    let data: [Video]
}

struct VideoListResponse: Decodable {
    let code: Int
    let message: String?
    let content: VideoListContent?
}
