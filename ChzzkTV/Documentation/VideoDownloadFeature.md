# Video Download Feature

## Overview

The video download feature allows users to download videos from the ChzzkTV platform for offline viewing. The implementation includes quality selection, progress tracking, background downloads, and comprehensive download management.

## Architecture

### Core Components

1. **VideoDownloadService** - Main service handling download operations
2. **VideoDownloadURLExtractor** - Utility for extracting downloadable URLs from HLS streams
3. **VideoDownloadViewModel** - View model managing download UI state
4. **UI Components** - Download buttons, options sheet, and downloads management view

### Key Features

- **Quality Selection**: Users can choose from available video qualities (1080p, 720p, 480p, etc.)
- **Progress Tracking**: Real-time download progress with visual indicators
- **Background Downloads**: Downloads continue when app is backgrounded
- **HLS Stream Processing**: Converts HLS streams to downloadable MP4 files
- **Storage Management**: Automatic cleanup and storage space validation
- **Download Management**: View, cancel, and delete downloads

## Implementation Details

### Services

#### VideoDownloadService
```swift
@MainActor
class VideoDownloadService: ObservableObject, VideoDownloadServiceProtocol {
    // Manages active and completed downloads
    // Handles background download sessions
    // Provides progress tracking and cancellation
}
```

#### VideoDownloadURLExtractor
```swift
class VideoDownloadURLExtractor {
    // Extracts downloadable URLs from HLS playlists
    // Processes M3U8 files to find best quality streams
    // Validates download URLs
}
```

### View Models

#### VideoDownloadViewModel
```swift
@MainActor
class VideoDownloadViewModel: ObservableObject {
    // Manages download UI state
    // Handles quality selection
    // Provides download progress information
}
```

### UI Components

#### VideoDownloadButton
- Compact download button for video cards
- Shows download state (pending, downloading, completed, failed)
- Handles download initiation and cancellation

#### VideoDownloadOptionsSheet
- Quality selection interface
- Video information display
- Download confirmation

#### DownloadsView
- Complete downloads management interface
- Active downloads with progress
- Completed downloads with file information

## Usage

### Adding Download Buttons to Video Views

```swift
struct VideoCard: View {
    let video: UIVideo
    @StateObject private var downloadViewModel = VideoDownloadViewModel()
    
    var body: some View {
        // ... existing video card content
        
        CompactVideoDownloadButton(
            video: video, 
            downloadViewModel: downloadViewModel
        )
    }
}
```

### Context Menu Integration

```swift
// In VideoHScrollView
Button(action: {
    downloadViewModel.startDownload(for: video)
}) {
    Label("Download", systemImage: "arrow.down.circle")
}
```

### Downloads Management

The Downloads tab is automatically added to the main navigation when users are logged in. It provides:

- List of active downloads with progress
- Completed downloads with file size information
- Swipe-to-delete functionality
- Download cancellation

## File Structure

```
ChzzkTV/
├── Services/
│   ├── VideoDownloadService.swift
│   └── ServiceProtocols.swift (updated)
├── Util/
│   └── VideoDownloadURLExtractor.swift
├── ViewModel/
│   └── VideoDownloadViewModel.swift
├── UI/
│   ├── Components/
│   │   ├── VideoDownloadButton.swift
│   │   └── VideoDownloadOptionsSheet.swift
│   ├── Views/
│   │   └── DownloadsView.swift
│   └── Screens/
│       ├── ContentView.swift (updated)
│       └── VideoDetailView.swift (updated)
├── Extensions/
│   └── EnvironmentValues+VideoDownload.swift
└── Tests/
    └── VideoDownloadTests.swift
```

## Storage

- Downloads are stored in the app's Documents/Downloads directory
- File names are sanitized versions of video titles with quality suffix
- Completed downloads are persisted in UserDefaults for app restart recovery
- Automatic cleanup of expired cache files (7 days)

## Error Handling

The system handles various error scenarios:

- **Network Unavailable**: Graceful failure with retry options
- **Insufficient Storage**: Pre-download storage validation
- **Invalid URLs**: URL validation and fallback mechanisms
- **Download Failures**: Error reporting with user-friendly messages
- **Cancelled Downloads**: Proper cleanup of partial downloads

## Performance Considerations

- **Background Downloads**: Uses URLSession background configuration
- **Memory Management**: Efficient handling of large video files
- **Progress Updates**: Throttled UI updates to prevent performance issues
- **Concurrent Downloads**: Limited concurrent downloads to prevent resource exhaustion

## Future Enhancements

Potential improvements for the download feature:

1. **Download Scheduling**: Allow users to schedule downloads for later
2. **WiFi-Only Downloads**: Option to restrict downloads to WiFi connections
3. **Download Queues**: Batch download management with priority queues
4. **External Storage**: Support for downloading to external storage devices
5. **Download Resume**: Resume interrupted downloads
6. **Bandwidth Limiting**: Control download speed to preserve network resources

## Testing

The implementation includes unit tests covering:

- Download service functionality
- URL extraction logic
- Error handling scenarios
- State management

Run tests using:
```bash
xcodebuild test -scheme ChzzkTV -destination 'platform=iOS Simulator,name=iPhone 15'
```

## Dependencies

The download feature relies on:

- **AVFoundation**: For video asset creation and export
- **Foundation**: For file management and networking
- **SwiftUI**: For user interface components
- **Combine**: For reactive programming patterns

## Security Considerations

- Downloads respect the original content's access controls
- Temporary files are properly cleaned up
- Download URLs are validated before processing
- User data privacy is maintained throughout the download process
