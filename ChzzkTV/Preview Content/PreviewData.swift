import Foundation
import SwiftUI

/// Preview providers and mock data for detail views
struct PreviewData {
    // MARK: - Live Preview
    @MainActor static func createPreviewLiveDetailView() -> some View {
        let previewStream = StreamPreview.createSampleUILiveStreams(count: 1).first!
        return LiveDetailView(stream: previewStream, viewModel: .init(channelService: PreviewChannelService()))
    }
    
    // MARK: - Video Preview
    @MainActor static func createPreviewVideoDetailView() -> some View {
        let previewVideo = StreamPreview.createSampleUIVideos(count: 1).first!
        return VideoDetailView(video: previewVideo, viewModel: .init(
            videoService: PreviewVideoService()
        ))
    }
} 
