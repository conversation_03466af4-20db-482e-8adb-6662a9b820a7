//
//  ForceLandscapeModifier.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/14/25.
//

import SwiftUI

struct ForceLandscapeModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
        #if os(iOS)
            .onAppear {
                AppDelegate.orientationLock = .landscape
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                    windowScene.requestGeometryUpdate(.iOS(interfaceOrientations: .landscape))
                    
                    if let rootViewController = windowScene.windows.first?.rootViewController {
                        rootViewController.setNeedsUpdateOfSupportedInterfaceOrientations()
                    }
                }
            }
            .onDisappear {
                AppDelegate.orientationLock = .all
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                    windowScene.requestGeometryUpdate(.iOS(interfaceOrientations: .all))
                    
                    if let rootViewController = windowScene.windows.first?.rootViewController {
                        rootViewController.setNeedsUpdateOfSupportedInterfaceOrientations()
                    }
                }
            }
        #endif
    }
}

extension View {
    func forceLandscape() -> some View {
        self.modifier(ForceLandscapeModifier())
    }
}
