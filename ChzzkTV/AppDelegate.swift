//
//  AppDelegate.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/14/25.
//

import UIKit

class AppDelegate: NSObject, UIApplicationDelegate {
    #if os(iOS)
    static var orientationLock = UIInterfaceOrientationMask.all

    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return AppDelegate.orientationLock
    }
    #endif
}
