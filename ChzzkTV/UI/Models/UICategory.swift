//
//  UICategory.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/4/25.
//

import Foundation

struct UICategory: Identifiable, Hashable {
    let id: String
    let type: String
    let value: String
    let imageUrl: URL?
    let channelCount: Int
    let viewerCount: Int
    let isNew: Bool
}

extension UICategory {
    var viewerCountFormatted: String {
        let formatted = CountFormatter.abbreviated(from: viewerCount)
        return String(format: NSLocalizedString("%@ watching", comment: "Number of concurrent viewers"), formatted)
    }
    
    var channelCountFormatted: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        let formattedCount = formatter.string(from: NSNumber(value: channelCount)) ?? "\(channelCount)"
        return String(format: NSLocalizedString("%@ channels", comment: "Number of live channels"), formattedCount)
    }
}

