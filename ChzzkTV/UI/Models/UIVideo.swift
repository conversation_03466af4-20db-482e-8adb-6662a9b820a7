import Foundation

struct UIVideo: Identifiable, Thumbnail, Equatable, Codable {
    let id: Int
    let uid: String?
    let title: String
    let imageUrl: URL?
    let duration: Int
    let timestamp: TimeInterval
    let category: String?
    var channel: UIChannel?
    var progress: Double?
    let isAbroad: Bool
    let isAdult: Bool
    
    init(id: Int, uid: String?, title: String, imageUrl: URL?, duration: Int, timestamp: TimeInterval, category: String?, channel: UIChannel?, progress: Double?, isAbroad: Bool = false, isAdult: Bool = false) {
        self.id = id
        self.uid = uid
        self.title = title
        self.imageUrl = imageUrl
        self.duration = duration
        self.timestamp = timestamp
        self.category = category
        self.channel = channel
        self.progress = progress
        self.isAbroad = isAbroad
        self.isAdult = isAdult
    }
}

extension UIVideo {
    // in thumbnail
    var relativeDate: String {
        let date = Date(timeIntervalSince1970: timestamp)
        return date.relativeString()
    }
    
    // in playback info
    var readableDate: String {
        let date = Date(timeIntervalSince1970: timestamp)
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        
        return dateFormatter.string(from: date)
    }
    
    var formattedDuration: String {
        let hours = duration / 3600
        let minutes = (duration % 3600) / 60
        let seconds = duration % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
}
