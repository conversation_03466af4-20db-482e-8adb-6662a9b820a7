import Foundation

struct UIChannel: Identifiable, Equatable, Hashable, Codable {
    let id: String
    let name: String
    let imageUrl: URL?
    let description: String?
    var isVerified: Bool?
    let followerCount: Int?
    let isLive: Bool?
    let isFollowing: Bool?
    
    init(id: String, name: String, imageUrl: URL?, description: String? = nil, isVerified: Bool? = nil, followerCount: Int? = nil, isLive: Bool? = nil, isFollowing: Bool? = nil) {
        self.id = id
        self.name = name
        self.imageUrl = imageUrl
        self.description = description
        self.isVerified = isVerified
        self.followerCount = followerCount
        self.isLive = isLive
        self.isFollowing = isFollowing
    }
}

extension UIChannel {
    var followerCountFormatted: String {
        guard let count = followerCount else {
            return ""
        }
        let formatted = CountFormatter.abbreviated(from: count)
        return String(format: NSLocalizedString("%@ followers", comment: "number of followers"), formatted)
    }
    
    func imageData() async -> Data? {
        guard let url = imageUrl else { return nil }
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            return data
        } catch {
            return nil
        }
    }
}
