//
//  CategoryLoadingView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/8/25.
//

import SwiftUI

struct CategoryLoadingView: View {
    private func columns(for width: CGFloat) -> [GridItem] {
#if os(tvOS)
        [GridItem(.adaptive(minimum: Constants.categoryCardWidth), alignment: .top)]
#else
        // Use actual screen width to determine layout
        // Portrait: width < 600, Landscape: width >= 600
        let grid = GridItem(.flexible(), alignment: .top)
        if width < 600 {
            return [grid, grid, grid]
        } else {
            return [grid, grid, grid, grid]
        }
#endif
    }
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVGrid(columns: columns(for: geometry.size.width), spacing: Constants.cardSpacing) {
                    ForEach(0..<12, id: \.self) { _ in
                        Button {
                            
                        } label: {
                            CategoryCard(category: nil)
                                .aspectRatio(3/4, contentMode: .fit)
                        }
#if os(tvOS)
                        .buttonStyle(.card)
#else
#endif
                    }
                }
                .padding(Constants.cardPadding)
            }
        }
    }
}
