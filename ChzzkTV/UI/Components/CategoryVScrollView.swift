//
//  CategoryVScrollView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/6/25.
//

import SwiftUI

struct CategoryVScrollView: View {
    let categories: [UICategory]
    @Binding var selectedCategory: UICategory?
    let onLoadMore: () async -> Void
    let isLoading: Bool
    
    private func columns(for width: CGFloat) -> [GridItem] {
#if os(tvOS)
        [GridItem(.adaptive(minimum: Constants.categoryCardWidth), alignment: .top)]
#else
        // Use actual screen width to determine layout
        // Portrait: width < 600, Landscape: width >= 600
        let grid = GridItem(.flexible(), alignment: .top)
        if width < 600 {
            return [grid, grid, grid]
        } else {
            return [grid, grid, grid, grid]
        }
#endif
    }
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVGrid(columns: columns(for: geometry.size.width), spacing: Constants.cardSpacing) {
                    ForEach(categories) { category in
                        Button {
                            selectedCategory = category
                        } label: {
                            CategoryCard(category: category)
                                .aspectRatio(3/4, contentMode: .fit)
                        }
#if os(tvOS)
                        .buttonStyle(.card)
#else
                        .buttonStyle(.plain)
#endif
                        .onAppear {
                            // Load more content when the last item appears
                            if category.id == categories.last?.id {
                                Task {
                                    await onLoadMore()
                                }
                            }
                        }
                    }
                }
                .padding(Constants.cardPadding)
            }
        }
    }
}

#Preview {
    CategoryVScrollView(
        categories: StreamPreview.createSampleUICategories(count: 20),
        selectedCategory: .constant(nil),
        onLoadMore: { },
        isLoading: false
    )
} 
