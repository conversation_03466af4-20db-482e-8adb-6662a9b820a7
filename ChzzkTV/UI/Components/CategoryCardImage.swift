//
//  CategoryCardImage.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/4/25.
//

import SwiftUI

struct CategoryCardImage: View {
    private let ratio: Double = 3/4
    let url: URL?

    var body: some View {
        if let url = url {
            let file = url.lastPathComponent
            if let fileName = file.components(separatedBy: ".").first,
               let image = UIImage(named: fileName) {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(ratio, contentMode: .fill)
            } else {
                CachedAsyncImage(url: url) { image in
                    image
                        .resizable()
                        .aspectRatio(ratio, contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(ratio, contentMode: .fill)
                }
                .clipped()
            }
        } else {
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .aspectRatio(ratio, contentMode: .fill)
        }
    }
}
