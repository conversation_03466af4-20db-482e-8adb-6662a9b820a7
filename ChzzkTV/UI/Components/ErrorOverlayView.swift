//
//  ErrorOverlayView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/20/25.
//

import SwiftUI

struct ErrorOverlayView: View {
    let message: String
    let onRetry: () -> Void
    let onDismiss: () -> Void
    
    #if os(tvOS)
    @FocusState private var focusedButton: FocusableButton?
    @Namespace private var overlayNamespace
    
    enum FocusableButton: Hashable {
        case retry, dismiss
    }
    #endif
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.7)
            VStack(spacing: 100) {
                Text(message)
                    .font(.headline)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                HStack(spacing: 50) {
                    But<PERSON>(action: onRetry) {
                        Text("Try Again")
                            .font(.headline)
                            .frame(minWidth: 200)
                            .padding(.vertical, 20)
                            .padding(.horizontal, 40)
                    }
                    #if os(tvOS)
                    .buttonStyle(.card)
                    #else
                    #endif
                    #if os(tvOS)
                    .focused($focusedButton, equals: .retry)
                    #endif
                    
                    But<PERSON>(action: onDismiss) {
                        Text("Dismiss")
                            .font(.headline)
                            .frame(minWidth: 200)
                            .padding(.vertical, 20)
                            .padding(.horizontal, 40)
                    }
                    #if os(tvOS)
                    .buttonStyle(.card)
                    #else
                    #endif
                    #if os(tvOS)
                    .focused($focusedButton, equals: .dismiss)
                    #endif
                }
            }
            .padding(40)
            #if os(tvOS)
            .focusSection()
            #endif
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .ignoresSafeArea()
        .onAppear {
            #if os(tvOS)
            // Set initial focus on retry button
            focusedButton = .retry
            #endif
        }
        #if os(tvOS)
        .onChange(of: focusedButton) { _, newValue in
            if newValue == nil {
                // If focus is lost, restore it to the retry button
                focusedButton = .retry
            }
        }
        .focusScope(overlayNamespace)
        #endif
    }
}

#Preview {
    ErrorOverlayView(
        message: "Something went wrong. Please try again.",
        onRetry: {},
        onDismiss: {}
    )
}
