import SwiftUI

struct EmptyHomeView: View {
    var body: some View {
        VStack(spacing: 40) {
            Image(systemName: "person.2.slash")
                .font(.system(size: 80))
                .foregroundColor(.secondary)
            
            VStack(spacing: 16) {
                Text("No Followed Channels")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("Follow your favorite channels to see their live streams and videos here")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            Button {
                NotificationCenter.default.post(
                    name: .navigateToTab,
                    object: "search"
                )
            } label: {
                Label("Find Channels", systemImage: "magnifyingglass")
                    .font(.headline)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 16)
                    .background(.chzzk)
                    .foregroundColor(.black)
                    .cornerRadius(12)
            }
            #if os(tvOS)
            .buttonStyle(.card)
            #else
            #endif
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.vertical, 100)
    }
} 
