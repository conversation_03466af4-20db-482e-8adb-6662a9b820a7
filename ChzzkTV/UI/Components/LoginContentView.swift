import SwiftUI

struct LoginContentView: View {
    @EnvironmentObject private var loginViewModel: QRCodeLoginViewModel
    @State private var showSignOutAlert = false
    
    var body: some View {
        Group {
            if loginViewModel.isUserLoggedIn {
                UserProfileView(
                    profile: loginViewModel.userProfile,
                    onSignOutTapped: {
                        showSignOutAlert = true
                    }
                )
                .alert("Sign Out", isPresented: $showSignOutAlert) {
                    But<PERSON>("Cancel", role: .cancel) { }
                    <PERSON><PERSON>("Sign Out", role: .destructive) {
                        loginViewModel.signOut()
                    }
                } message: {
                    Text("Are you sure you want to sign out?")
                }
            } else {
                QRCodeLoginView()
            }
        }
    }
}

#Preview {
    LoginContentView()
        .environmentObject(QRCodeLoginViewModel())
} 
