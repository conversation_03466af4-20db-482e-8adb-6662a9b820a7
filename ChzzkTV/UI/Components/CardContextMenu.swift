import SwiftUI

struct CardContextMenu: View {
    let channel: UIChannel
    @Binding var followStatus: Bool
    
    @Environment(\.channelService) private var channelService
    
    var onFollowStatusChanged: ((Bool) -> Void)?
    
    var body: some View {
        Group {
            Button {
                Task {
                    let newState = await channelService.toggleFollow(channelId: channel.id)
                    followStatus = newState
                    onFollowStatusChanged?(newState)
                }
            } label: {
                Label(followStatus ? "Unfollow" : "Follow",
                      systemImage: followStatus ? "heart.fill" : "heart")
            }
            
            Button {
                NotificationCenter.default.post(
                    name: .navigateToChannel,
                    object: channel.id
                )
            } label: {
                Label("Go to channel", systemImage: "person.circle")
            }
        }
    }
}

#Preview("Not followed, Not Hidden") {
    CardContextMenu(
        channel: StreamPreview.sampleUIChannel,
        followStatus: .constant(false)
    )
}

#Preview("Followed, Hidden") {
    CardContextMenu(
        channel: StreamPreview.sampleUIChannel,
        followStatus: .constant(true)
    )
}

#Preview("Hidden is hidden") {
    CardContextMenu(
        channel: StreamPreview.sampleUIChannel,
        followStatus: .constant(true)
    )
}
