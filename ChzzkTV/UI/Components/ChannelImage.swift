//
//  ChannelImage.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/4/25.
//

import SwiftUI

struct ChannelImage: View {
    let url: URL

    var body: some View {
        CachedAsyncImage(url: url) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Circle()
                .fill(Color.gray.opacity(0.3))
        }
        .frame(width: Constants.channelImageSize, height: Constants.channelImageSize)
        .clipShape(Circle())
    }
}
