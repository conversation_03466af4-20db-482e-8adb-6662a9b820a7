import SwiftUI

struct CategoryCard: View {
    let category: UICategory?
    
    var body: some View {
        VStack(spacing: 0) {
            if let category = category {
                CategoryCardImage(url: category.imageUrl)
                
                VStack(alignment: .leading, spacing: 4) {
                    titleView
                        .bold()
#if os(tvOS)
                        .font(.body)
#else
                        .font(.subheadline)
#endif
                        .lineLimit(2, reservesSpace: true)
                        .minimumScaleFactor(0.8)
                    
                    if ScreenDetection.isSmallScreenReliable() {
                        Text(category.viewerCountFormatted)
                            .font(.caption)
                            .foregroundStyle(.secondary)
                            .lineLimit(1)
                            .minimumScaleFactor(0.8)
                    } else {
                        Text(category.channelCountFormatted)
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                }
                .padding(Constants.cardPadding)
                .frame(maxWidth: .infinity, alignment: .leading)
            } else {
                // Placeholder for loading state
                CategoryCardImage(url: nil)
                
                VStack(alignment: .leading, spacing: 4) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 20)
                        .cornerRadius(4)
                    
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 14)
                        .frame(width: 80)
                        .cornerRadius(4)
                }
                .padding(Constants.cardPadding)
                .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
#if os(tvOS)
        .frame(width: Constants.categoryCardWidth)
        .background(.ultraThinMaterial)
#else
        .frame(maxWidth: .infinity)
        .background(Color(.tertiarySystemGroupedBackground))
#endif
        .clipShape(.rect(cornerRadius: Constants.cornerRadius))
        .overlay(alignment: .topLeading) {
            if let category = category, category.viewerCount > 0 {
#if os(iOS)
                if ScreenDetection.isSmallScreenReliable() {
                    if category.isNew {
                        newBadge
                            .padding(Constants.cardPadding/2)
                    }
                } else {
                    ViewerCountOverlayView(label: category.viewerCountFormatted)
                        .padding(Constants.cardPadding)
                }
#else
                ViewerCountOverlayView(label: category.viewerCountFormatted)
                    .padding(Constants.cardPadding)
#endif
            }
        }
    }
    
    var titleView: some View {
        if let category = category, category.isNew {
            if ScreenDetection.isSmallScreenReliable() {
                Text(category.value)
            } else {
                Text(category.value) +
                Text(" ") +
                Text(Image(systemName: "n.circle.fill"))
                    .foregroundStyle(.red)
            }
        } else {
            Text(category?.value ?? "")
        }
    }
    
    @ViewBuilder
    var newBadge: some View {
        let textComponent = Text("NEW")
            .font(.caption)
            .fontWeight(.bold)
            
        if #available(iOS 26.0, tvOS 26.0, *) {
            textComponent
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .foregroundStyle(.red)
                .glassEffect(.regular)
        } else {
            textComponent
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .foregroundStyle(.white)
                .background(Color.red)
                .cornerRadius(4)
        }
    }
}

#Preview("Loaded") {
    CategoryCard(category: StreamPreview.sampleUICategory)
        .frame(width: Constants.categoryCardWidth, height: Constants.categoryImageHeight + 200)
        .padding()
}

#Preview("Loading") {
    CategoryCard(category: nil)
        .frame(width: Constants.categoryCardWidth, height: Constants.categoryImageHeight + 200)
        .padding()
} 
