import SwiftUI

struct UserProfileView: View {
    let profile: QRCodeLoginViewModel.UserProfile?
    let onSignOutTapped: () -> Void
    
    @State private var imageLoaded = false
    @State private var profileImage: UIImage?
    
    var body: some View {
        VStack(alignment: .center, spacing: 100) {
            // Profile Image
            ProfileImageView(profileImage: profileImage, imageLoaded: imageLoaded)
            
            // Nickname
            Text(profile?.nickname ?? "")
                .font(.title)
                .bold()
                .multilineTextAlignment(.center)
            
            Spacer()
            
            // Sign Out Button
            Button {
                onSignOutTapped()
            } label: {
                Label("Sign Out", systemImage: "rectangle.portrait.and.arrow.right")
                    .bold()
                    .frame(maxWidth: 400)
                    .foregroundColor(.white)
                    .padding(.vertical, 30)
#if os(tvOS)
                    .background(.red.opacity(0.8))
#endif
            }
#if os(tvOS)
            .buttonStyle(.card)
#else
            .buttonStyle(.borderedProminent)
#endif
            .padding(.bottom, 40)
            .tint(.red.opacity(0.8))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
        .padding(60)
        .onAppear {
            loadProfileImage()
        }
    }
    
    private func loadProfileImage() {
        guard let profile = profile, let imageUrlString = profile.profileImageUrl,
              let imageUrl = URL(string: imageUrlString) else {
            imageLoaded = true
            return
        }
        
        URLSession.shared.dataTask(with: imageUrl) { data, response, error in
            DispatchQueue.main.async {
                if let data = data, error == nil, let image = UIImage(data: data) {
                    self.profileImage = image
                }
                self.imageLoaded = true
            }
        }.resume()
    }
}

// Extract profile image to a separate component
struct ProfileImageView: View {
    let profileImage: UIImage?
    let imageLoaded: Bool
    @State private var pulseScale: CGFloat = 0.9
    
    var body: some View {
        Group {
            if let image = profileImage, imageLoaded {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(
                        width: Constants.profileImageSize,
                        height: Constants.profileImageSize
                    )
                    .clipShape(Circle())
                    .shadow(radius: 10)
            } else {
                Circle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(
                        width: Constants.profileImageSize,
                        height: Constants.profileImageSize
                    )
                    .shadow(radius: 5)
                    .overlay {
                        if !imageLoaded {
                            // Simple pulsing animation instead of ProgressView
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                                .scaleEffect(pulseScale)
                                .animation(
                                    .easeInOut(duration: 1.0)
                                    .repeatForever(autoreverses: true),
                                    value: pulseScale
                                )
                                .onAppear {
                                    pulseScale = 1.1
                                }
                        } else {
                            Image(systemName: "person.crop.circle.fill")
                                .resizable()
                                .frame(width: 100, height: 100)
                                .foregroundColor(.gray)
                        }
                    }
            }
        }
        #if os(tvOS)
        .focusable(false)
        #endif
    }
}

#Preview {
    UserProfileView(
        profile: QRCodeLoginViewModel.UserProfile(
            nickname: "TestUser",
            profileImageUrl: nil
        ),
        onSignOutTapped: {}
    )
} 
