import SwiftUI

struct LoadingView: View {
    let message: String
    
    init(_ message: String = "Loading...") {
        self.message = message
    }
    
    var body: some View {
        HStack {
            Spacer()
            VStack(spacing: 8) {
                // Simple animated dots instead of ProgressView
                HStack(spacing: 4) {
                    ForEach(0..<3, id: \.self) { index in
                        Circle()
                            .fill(.secondary)
                            .frame(width: 8, height: 8)
                            .scaleEffect(animationScale)
                            .animation(
                                .easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                                value: animationScale
                            )
                    }
                }
                .onAppear {
                    animationScale = 1.2
                }

                Text(message)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            Spacer()
        }
    }

    @State private var animationScale: CGFloat = 0.8
}

#Preview {
    LoadingView("Loading more videos...")
} 