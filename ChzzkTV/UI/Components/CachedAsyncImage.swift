//
//  CachedAsyncImage.swift
//  ChzzkTV
//
//  Created by Augment Agent on 8/27/25.
//

import SwiftUI
import os.log

/// A cached version of AsyncImage that uses disk caching for better performance
struct CachedAsyncImage<Content: View, Placeholder: View>: View {
    private let url: URL?
    private let content: (Image) -> Content
    private let placeholder: () -> Placeholder
    
    @State private var image: UIImage?
    @State private var isLoading = false
    @State private var loadingTask: Task<Void, Never>?
    
    private let logger = Logger(subsystem: "ChzzkTV", category: "CachedAsyncImage")
    
    init(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) {
        self.url = url
        self.content = content
        self.placeholder = placeholder
    }
    
    var body: some View {
        Group {
            if let image = image {
                content(Image(uiImage: image))
            } else {
                placeholder()
                    .onAppear {
                        loadImage()
                    }
                    .onDisappear {
                        cancelLoading()
                    }
            }
        }
        .onChange(of: url) { _, newURL in
            // Reset state when URL changes
            image = nil
            cancelLoading()
            if newURL != nil {
                loadImage()
            }
        }
    }
    
    private func loadImage() {
        guard let url = url, !isLoading else { return }
        
        isLoading = true
        
        loadingTask = Task {
            // First check cache
            if let cachedImage = await ImageCacheManager.shared.getImage(for: url) {
                await MainActor.run {
                    self.image = cachedImage
                    self.isLoading = false
                }
                return
            }
            
            // Download and cache if not found
            if let downloadedImage = await ImageCacheManager.shared.downloadAndCacheImage(from: url) {
                await MainActor.run {
                    self.image = downloadedImage
                    self.isLoading = false
                }
            } else {
                await MainActor.run {
                    self.isLoading = false
                }
                logger.warning("Failed to load image from URL: \(url.absoluteString)")
            }
        }
    }
    
    private func cancelLoading() {
        loadingTask?.cancel()
        loadingTask = nil
        isLoading = false
    }
}

// MARK: - Convenience Initializers

extension CachedAsyncImage where Content == Image, Placeholder == Color {
    /// Convenience initializer with default placeholder
    init(url: URL?) {
        self.init(
            url: url,
            content: { $0 },
            placeholder: { Color.gray.opacity(0.3) }
        )
    }
}

extension CachedAsyncImage where Placeholder == Color {
    /// Convenience initializer with custom content and default placeholder
    init(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content
    ) {
        self.init(
            url: url,
            content: content,
            placeholder: { Color.gray.opacity(0.3) }
        )
    }
}

// MARK: - Preview

#Preview("With URL") {
    CachedAsyncImage(url: URL(string: "https://picsum.photos/200")) { image in
        image
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: 100, height: 100)
            .clipShape(Circle())
    } placeholder: {
        Circle()
            .fill(Color.gray.opacity(0.3))
            .frame(width: 100, height: 100)
    }
}

#Preview("Without URL") {
    CachedAsyncImage(url: nil) { image in
        image
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: 100, height: 100)
            .clipShape(Circle())
    } placeholder: {
        Circle()
            .fill(Color.gray.opacity(0.3))
            .frame(width: 100, height: 100)
    }
}
