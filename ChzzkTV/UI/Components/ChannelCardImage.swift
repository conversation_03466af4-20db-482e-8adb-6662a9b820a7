//
//  ChannelCardImage.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/4/25.
//

import SwiftUI

struct ChannelCardImage: View {
    let url: URL?

    var body: some View {
        if let url = url {
            CachedAsyncImage(url: url) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: Constants.channelCardSize, height: Constants.channelCardSize)
                    .clipShape(Circle())
            } placeholder: {
                Circle()
                    .foregroundColor(.gray.opacity(0.3))
                    .frame(width: Constants.channelCardSize, height: Constants.channelCardSize)
            }
        } else {
            Image(systemName: "person.circle")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: Constants.channelCardSize, height: Constants.channelCardSize)
                .background(.gray.opacity(0.3))
                .hoverEffect(.highlight)
                .clipShape(Circle())
                .foregroundStyle(.gray)
        }
    }
}

#Preview {
    ChannelCardImage(url: URL(string: "https://picsum.photos/200")!)
}
