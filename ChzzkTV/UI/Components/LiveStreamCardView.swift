import SwiftUI

struct LiveStreamCardView: View {
    let stream: UILiveStream
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            ZStack(alignment: .bottomLeading) {
                // Thumbnail
                if let url = stream.imageUrl {
                    AsyncImage(url: url) { image in
                        image
                            .resizable()
                            .scaledToFill()
                    } placeholder: {
                        Rectangle()
                            .foregroundColor(.gray.opacity(0.3))
                    }
                }
                
                // Overlay gradient
                LinearGradient(
                    gradient: Gradient(colors: [Color.black.opacity(0.7), Color.black.opacity(0)]),
                    startPoint: .bottom,
                    endPoint: .center
                )
                
                // Live indicator and title
                VStack(alignment: .leading, spacing: 4) {
                    Text("LIVE")
                        .font(.caption)
                        .fontWeight(.bold)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.red)
                        .cornerRadius(4)
                        .foregroundStyle(.white)
                    
                    Spacer()
                    
                    Text(stream.viewerCountFormatted)
                        .font(.caption)
                        .foregroundColor(.white)
                    
                    Text(stream.title)
                        .font(.body)
                        .foregroundColor(.white)
                        .lineLimit(2)
                }
                .padding()
            }
        }
#if os(tvOS)
        .buttonStyle(.card)
#else
        .buttonStyle(.plain)
#endif
    }
}

#Preview {
    LiveStreamCardView(
        stream: StreamPreview.sampleUILiveStream,
        onTap: {
            
        }
    )
    .frame(width: 200, height: 100)
}
