import SwiftUI

struct ErrorView: View {
    let title: String
    let message: String
    let isRetrying: Bool
    let retryAction: () -> Void
    
    var body: some View {
        HStack {
            Spacer()
            VStack(spacing: 30) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.largeTitle)
                    .foregroundColor(.orange)
                
                Text(title)
                    .font(.headline)
                
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                <PERSON><PERSON>("Try Again", action: retryAction)
                    .buttonStyle(.bordered)
                    .disabled(isRetrying)
            }
            .padding()
            Spacer()
        }
    }
}

#Preview {
    ErrorView(
        title: "Failed to load videos",
        message: "There was an error loading the videos. Please try again.",
        isRetrying: false,
        retryAction: {}
    )
} 
