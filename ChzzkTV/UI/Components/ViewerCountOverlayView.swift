//
//  ViewerCountOverlayView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/6/25.
//

import SwiftUI

struct ViewerCountOverlayView: View {
    let label: String

    var body: some View {
        if #available(iOS 26.0, tvOS 26.0, *) {
            content()
                .glassEffect()
        } else {
            content(textColor: .white)
                .background {
                    Capsule()
                        .fill(.black.opacity(0.7))
                }
        }
    }
    
    @ViewBuilder
    func content(textColor: Color? = nil) -> some View {
        HStack(spacing: Constants.viewerCountSpacing) {
            Circle()
                .fill(.red)
                .frame(width: Constants.viewerCountIndicatorSize,
                       height: Constants.viewerCountIndicatorSize)
            if let color = textColor {
                Text(label)
                    .font(.caption2)
                    .foregroundStyle(color)
            } else {
                Text(label)
                    .font(.caption2)
            }
        }
        .padding(.horizontal, Constants.viewerCountPaddingH)
        .padding(.vertical, Constants.viewerCountPaddingV)
    }
}

#Preview {
    VStack {
        ViewerCountOverlayView(label: "1234 viewers")
            .padding()
            .background(Color.black)
        
        ViewerCountOverlayView(label: "1234 viewers")
            .padding()
            .background(Color.white)
    }
}
