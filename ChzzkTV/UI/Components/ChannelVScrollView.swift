//
//  ChannelVScrollView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/31/25.
//

import SwiftUI

struct ChannelVScrollView: View {
    let channels: [UIChannel]
    
    private func columns(for width: CGFloat) -> [GridItem] {
        #if os(tvOS)
        [GridItem(.adaptive(minimum: Constants.channelCardSize), spacing: 100, alignment: .top)]
        #else
        let gridItem = GridItem(.flexible(), alignment: .top)
        
        if width < 600 {
            return [gridItem, gridItem, gridItem]
        } else {
            return [gridItem, gridItem, gridItem, gridItem, gridItem, gridItem]
        }
        #endif
    }
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView(.vertical, showsIndicators: false) {
                LazyVGrid(columns: columns(for: geometry.size.width), spacing: Constants.cardSpacing) {
                    ForEach(channels) { channel in
//                        Button {
//                            onSelectedChannel(channel)
//                        } label: {
//                            ChannelCard(channel: channel)
//                                .frame(width: Constants.channelCardSize)
//                        }
//                        .buttonBorderShape(.circle)
                        NavigationLink(value: channel) {
                            ChannelCard(channel: channel)
                                .frame(width: Constants.channelCardSize)
                        }
                    }
                }
#if os(tvOS)
                .buttonStyle(.borderless)
#else
                .buttonStyle(.plain)
#endif
                .padding(Constants.cardPadding)
            }
        }
    }
}

#Preview {
    ChannelVScrollView(channels: StreamPreview.createSampleUIChannels(count: 20))
}
