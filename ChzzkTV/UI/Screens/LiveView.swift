import SwiftUI
import AVKit
import SwiftData

struct LiveView: View {
    @StateObject private var popularViewModel: LiveViewModel
    @StateObject private var latestViewModel: LiveViewModel
    @State private var selectedStream: UILiveStream? = nil
    @State private var showDetailView = false
    @State private var sortOption: LiveSortType = .popular
    @State private var isViewActive = false
    @State private var scrollToTop = false

    @Environment(\.scenePhase) private var scenePhase
    @Environment(\.channelService) private var channelService
    
    init(
        liveService: LiveServiceProtocol
    ) {
        _popularViewModel = .init(wrappedValue: LiveViewModel(
            liveService: liveService,
            sortOption: .popular)
        )
        _latestViewModel = .init(wrappedValue: LiveViewModel(
            liveService: liveService,
            sortOption: .latest)
        )
    }
    
    var body: some View {
        contentView
            .onChange(of: sortOption) {
                Task {
                    await loadStreams(sortOption: sortOption)
                }
            }
            .task {
                await loadStreams(sortOption: sortOption)
            }
            .onReceive(NotificationCenter.default.publisher(for: .scrollToTopLiveView)) { _ in
                // Trigger scroll to top
                scrollToTop.toggle()
            }
            .onAppear {
                isViewActive = true
            }
            .onDisappear {
                isViewActive = false
            }
            .onChange(of: scenePhase) { _, newPhase in
                if newPhase == .active, isViewActive {
                    Task {
                        await loadStreams(sortOption: sortOption)
                    }
                }
            }
            .fullScreenCover(item: $selectedStream) { stream in
                LiveDetailView(stream: stream, viewModel: .init(channelService: channelService))
            }
    }
    
    @ViewBuilder
    private var contentView: some View {
        #if os(tvOS)
        TabView(selection: $sortOption) {
            Tab(String(localized: "Popular"), systemImage: "flame.fill", value: LiveSortType.popular) {
                LiveStreamVScrollView(
                    streams: popularViewModel.streams,
                    selectedStream: $selectedStream,
                    onLoadMore: { await popularViewModel.loadMoreStreamsIfNeeded() },
                    isLoading: popularViewModel.isLoading,
                    onStreamHidden: { streamToHide in
                        popularViewModel.streams = popularViewModel.streams.filter({ $0.id != streamToHide.id })
                    }
                )
            }
            
            Tab(String(localized: "Latest"), systemImage: "bell.fill", value: LiveSortType.latest) {
                LiveStreamVScrollView(
                    streams: latestViewModel.streams,
                    selectedStream: $selectedStream,
                    onLoadMore: { await latestViewModel.loadMoreStreamsIfNeeded() },
                    isLoading: latestViewModel.isLoading,
                    onStreamHidden: { streamToHide in
                        latestViewModel.streams = latestViewModel.streams.filter({ $0.id != streamToHide.id })
                    }
                )
            }
        }
        .tabViewStyle(.tabBarOnly)
        #else
        VStack(spacing: 0) {
            // Segmented picker at the top
            Picker(String(localized: "Sort"), selection: $sortOption) {
                Text(String(localized: "Popular")).tag(LiveSortType.popular)
                Text(String(localized: "Latest")).tag(LiveSortType.latest)
            }
            .pickerStyle(.segmented)
            .padding(.horizontal)
            .padding(.bottom, 16)

            // TabView with page style for swipe gestures
            TabView(selection: $sortOption) {
                ScrollViewReader { proxy in
                    LiveStreamVScrollView(
                        streams: popularViewModel.streams,
                        selectedStream: $selectedStream,
                        onLoadMore: { await popularViewModel.loadMoreStreamsIfNeeded() },
                        isLoading: popularViewModel.isLoading,
                        onStreamHidden: { streamToHide in
                            popularViewModel.streams = popularViewModel.streams.filter({ $0.id != streamToHide.id })
                        }
                    )
                    .onChange(of: scrollToTop) { _, _ in
                        if sortOption == .popular {
                            withAnimation(.easeInOut(duration: 0.5)) {
                                proxy.scrollTo("top", anchor: .top)
                            }
                        }
                    }
                }
                .tag(LiveSortType.popular)

                ScrollViewReader { proxy in
                    LiveStreamVScrollView(
                        streams: latestViewModel.streams,
                        selectedStream: $selectedStream,
                        onLoadMore: { await latestViewModel.loadMoreStreamsIfNeeded() },
                        isLoading: latestViewModel.isLoading,
                        onStreamHidden: { streamToHide in
                            latestViewModel.streams = latestViewModel.streams.filter({ $0.id != streamToHide.id })
                        }
                    )
                    .onChange(of: scrollToTop) { _, _ in
                        if sortOption == .latest {
                            withAnimation(.easeInOut(duration: 0.5)) {
                                proxy.scrollTo("top", anchor: .top)
                            }
                        }
                    }
                }
                .tag(LiveSortType.latest)
            }
            .tabViewStyle(.page(indexDisplayMode: .never))
            .ignoresSafeArea()
        }
        #endif
    }
    
    func loadStreams(sortOption: LiveSortType, reset: Bool = false) async {
        switch (sortOption) {
        case .popular:
            await popularViewModel.loadStreams(reset: reset)
        case .latest:
            await latestViewModel.loadStreams(reset: reset)
        }
    }
}

#Preview {
    LiveView(
        liveService: PreviewLiveService()
    )
}
