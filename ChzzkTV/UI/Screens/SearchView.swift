import SwiftUI

struct SearchView: View {
    @StateObject private var viewModel: SearchViewModel
    @State private var searchText = ""
    
    private let channelService: ChannelServiceProtocol
    
    init(viewModel: SearchViewModel, channelService: ChannelServiceProtocol) {
        _viewModel = .init(wrappedValue: viewModel)
        self.channelService = channelService
    }
    
    var body: some View {
        NavigationStack {
            contentView
                .navigationDestination(for: UIChannel.self) { channel in
                    ChannelView(
                        channelId: channel.id,
                        viewModel: ChannelViewModel(
                            channelService: channelService
                        )
                    )
                }
#if os(tvOS)
                .searchSuggestions {
                    ForEach(viewModel.suggestions, id: \.self) { suggestion in
                        Text(suggestion)
                    }
                }
                .padding(.top)
#endif
        }
    }
    
    private var contentView: some View {
        ChannelVScrollView(
            channels: viewModel.channels
        )
        .searchable(text: $searchText)
        .onChange(of: searchText) { _, newValue in
            if newValue.isEmpty {
                viewModel.channels = []
                viewModel.suggestions = []
                return
            }
            
            Task {
                await viewModel.search(query: newValue)
            }
        }
    }
}
