//
//  ContentView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/15/25.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @StateObject private var viewModel = ContentViewModel()
    @EnvironmentObject private var loginViewModel: QRCodeLoginViewModel
    @Environment(\.channelService) private var channelService
    @Environment(\.liveService) private var liveService
    @Environment(\.searchService) private var searchService
    @Environment(\.categoryService) private var categoryService
    @Environment(\.videoService) private var videoService
    
    @State private var isInitialized = false

    private var shouldShowLoginTab: Bool {
        #if os(tvOS)
        return true
        #else
        return !loginViewModel.isUserLoggedIn
        #endif
    }

    var body: some View {
        Group {
            TabView(selection: Binding(
                get: { viewModel.selectedTab },
                set: { viewModel.setSelectedTab(viewModel.handleTabSelection($0, isLoggedIn: loginViewModel.isUserLoggedIn)) }
            )) {
                if shouldShowLoginTab {
                    Tab(loginLabel, systemImage: "person.crop.circle.fill", value: Tabs.login) {
                        LoginContentView()
                            .environmentObject(loginViewModel)
                    }
                }
                if loginViewModel.isUserLoggedIn {
                    Tab(String(localized: "Home"), systemImage: "house.fill", value: Tabs.home) {
                        HomeView(
                            channelService: channelService,
                            liveService: liveService,
                            videoService: videoService,
                            loginViewModel: loginViewModel
                        )
                    }
                }
                
                Tab(String(localized: "Live"), systemImage: "play.tv.fill", value: Tabs.live) {
                    LiveView(
                        liveService: liveService
                    )
                }
                
                Tab(String(localized: "Category"), systemImage: "square.grid.2x2", value: Tabs.category) {
                    CategoryView(viewModel: .init(
                        categoryService: categoryService
                    ))
                }

                #if !os(tvOS)
                if loginViewModel.isUserLoggedIn {
                    Tab(String(localized: "Downloads"), systemImage: "arrow.down.circle", value: Tabs.downloads) {
                        DownloadsView()
                    }
                }
                #endif

                Tab(value: Tabs.search, role: .search) {
                    SearchView(
                        viewModel: .init(searchService: searchService),
                        channelService: channelService
                    )
                }
            }
#if os(tvOS)
            .tabViewStyle(.sidebarAdaptable)
#else
            .tabViewStyle(.tabBarOnly)
#endif
        }
        .onChange(of: loginViewModel.isUserLoggedIn) { _, isLoggedIn in
            // If user logs out while on Home or Downloads tab, redirect to Live tab
            if !isLoggedIn && (viewModel.selectedTab == .home || viewModel.selectedTab == .downloads) {
                viewModel.setSelectedTab(.live)
            }
        }
        .sheetOrFullScreen(
            isPresented: Binding(
                get: { viewModel.channelToOpen != nil },
                set: { _ in viewModel.clearChannelNavigation() }
            )
        ) {
            ChannelView(channelId: viewModel.channelToOpen ?? "", viewModel: ChannelViewModel(
                channelService: channelService
            ))
        }
    }
    
    var loginLabel: String {
        if loginViewModel.isUserLoggedIn {
            return String(localized: "Profile")
        } else {
            return String(localized: "Login")
        }
    }
}

struct PreviewContentView: View {
    let selectedTab: Tabs
    
    var body: some View {
        let schema = Schema([
        ])
        let config = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
        let container = try! ModelContainer(for: schema, configurations: [config])
        
        return ContentView()
            .modelContainer(container)
            .environment(\.channelService, PreviewChannelService())
            .environment(\.liveService, PreviewLiveService())
            .environment(\.searchService, PreviewSearchService())
            .environment(\.categoryService, PreviewCategoryService())
            .environment(\.videoService, PreviewVideoService())
            .environmentObject(QRCodeLoginViewModel())
            .onAppear {
                UserDefaults.standard.set(selectedTab.rawValue, forKey: "ContentView.selectedTab")
            }
    }
}

#Preview("Home Tab") {
    PreviewContentView(selectedTab: .home)
}

#Preview("Live Tab") {
    PreviewContentView(selectedTab: .live)
}

#Preview("Search Tab") {
    PreviewContentView(selectedTab: .search)
}
