//
//  LiveDetailView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/16/25.
//

import SwiftUI

struct LiveDetailView: View {
    let stream: UILiveStream
    @StateObject private var viewModel: LiveDetailViewModel
    @Environment(\.dismiss) private var dismiss
    
    init(stream: UILiveStream, viewModel: LiveDetailViewModel) {
        self.stream = stream
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    var body: some View {
        ZStack {
            LiveStreamPlayerView(
                player: viewModel.player,
                channel: stream.channel,
                availableQualities: viewModel.availableQualities,
                currentQuality: viewModel.currentQuality,
                onQualityChange: { quality in
                    Task {
                        await viewModel.changeQuality(quality)
                    }
                }
            )
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .ignoresSafeArea()
            .forceLandscape()
            
            if viewModel.error != nil {
                ErrorOverlayView(
                    message: "Unable to load stream\n\(viewModel.error?.localizedDescription ?? "")",
                    onRetry: {
                        Task {
                            viewModel.resetError()
                            if let channelId = stream.channel?.id {
                                await viewModel.loadStreamDetails(channelId: channelId)
                            }
                        }
                    },
                    onDismiss: {
                        dismiss()
                    }
                )
            }
        }
        #if os(tvOS)
        .onPlayPauseCommand {
            viewModel.togglePlayback()
        }
        #endif
        .task {
            print("LiveDetailView task started")
            if let channelId = stream.channel?.id {
                await viewModel.loadStreamDetails(channelId: channelId)
            }
        }
        .onAppear {
            print("LiveDetailView appeared")
        }
        .onDisappear {
            print("LiveDetailView disappeared - cleaning up")
            viewModel.cleanUp()
        }
    }
}

#Preview {
    PreviewData.createPreviewLiveDetailView()
}
