//
//  DownloadsView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import SwiftUI

#if !os(tvOS)
struct DownloadsView: View {
    @ObservedObject private var downloadService = VideoDownloadService.shared
    @State private var selectedDownload: VideoDownloadInfo?
    @State private var showingDeleteAlert = false
    
    var body: some View {
        VStack {
                if downloadService.activeDownloads.isEmpty &&
                   downloadService.completedDownloads.isEmpty {
                    // Empty state
                    VStack(spacing: 16) {
                        Image(systemName: "arrow.down.circle")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        
                        Text("No Downloads")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Text("Downloaded videos will appear here")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    List {
                        // Active Downloads Section
                        if !downloadService.activeDownloads.isEmpty {
                            Section("Active Downloads") {
                                ForEach(Array(downloadService.activeDownloads.values), id: \.video.id) { download in
                                    ActiveDownloadRow(download: download, onCancel: {
                                        downloadService.cancelDownload(for: download.video.id)
                                    })
                                }
                            }
                        }
                        
                        // Completed Downloads Section
                        if !downloadService.completedDownloads.isEmpty {
                            Section("Downloaded Videos") {
                                ForEach(Array(downloadService.completedDownloads.values), id: \.video.id) { download in
                                    CompletedDownloadRow(
                                        download: download,
                                        fileURL: downloadService.getDownloadedFileURL(for: download),
                                        onDelete: {
                                            selectedDownload = download
                                            showingDeleteAlert = true
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
        }
        .refreshable {
            // Refresh the downloads list
            downloadService.objectWillChange.send()
        }
        .alert("Delete Download", isPresented: $showingDeleteAlert) {
            Button("Delete", role: .destructive) {
                if let download = selectedDownload {
                    try? downloadService.deleteDownloadedVideo(for: download.video.id)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            if let download = selectedDownload {
                Text("Are you sure you want to delete '\(download.video.title)'?")
            }
        }
    }
}

struct ActiveDownloadRow: View {
    let download: VideoDownloadInfo
    let onCancel: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // Thumbnail
            CachedAsyncImage(url: download.video.imageUrl) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
            }
            .frame(width: 80, height: 45)
            .clipShape(RoundedRectangle(cornerRadius: 6))
            
            // Video Info
            VStack(alignment: .leading, spacing: 4) {
                Text(download.video.title)
                    .font(.headline)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                if let channel = download.video.channel {
                    Text(channel.name)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                // Download Progress
                VStack(alignment: .leading, spacing: 2) {
                    Text(download.state.displayText)
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    if case .downloading(let progress) = download.state {
                        ProgressView(value: progress)
                            .progressViewStyle(.circular)
                            .scaleEffect(0.8)
                    } else if case .processing = download.state {
                        ProgressView()
                            .progressViewStyle(.circular)
                            .scaleEffect(0.8)
                    }
                }
            }
            
            Spacer()

            // Progress and Cancel Button
            HStack(spacing: 8) {
                // Circular progress indicator
                if case .downloading(let progress) = download.state {
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.3), lineWidth: 3)
                            .frame(width: 32, height: 32)

                        Circle()
                            .trim(from: 0, to: progress)
                            .stroke(Color.blue, style: StrokeStyle(lineWidth: 3, lineCap: .round))
                            .frame(width: 32, height: 32)
                            .rotationEffect(.degrees(-90))

                        Text("\(Int(progress * 100))%")
                            .font(.caption2)
                            .fontWeight(.medium)
                    }
                } else if case .processing = download.state {
                    ProgressView()
                        .progressViewStyle(.circular)
                        .scaleEffect(0.8)
                        .frame(width: 32, height: 32)
                }

                // Cancel Button
                Button(action: onCancel) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.red)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.vertical, 4)
    }
}

struct CompletedDownloadRow: View {
    let download: VideoDownloadInfo
    let fileURL: URL?
    let onDelete: () -> Void

    private var downloadRowContent: some View {
        HStack(spacing: 12) {
            // Thumbnail
            CachedAsyncImage(url: download.video.imageUrl) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
            }
            .frame(width: 80, height: 45)
            .clipShape(RoundedRectangle(cornerRadius: 6))

            // Video Info
            VStack(alignment: .leading, spacing: 4) {
                Text(download.video.title)
                    .font(.headline)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)

                if let channel = download.video.channel {
                    Text(channel.name)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                HStack {
                    Text(download.quality.displayName)
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .clipShape(RoundedRectangle(cornerRadius: 4))

                    Text(download.formattedFileSize)
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()
                }
            }

            Spacer()

            // Downloaded indicator
            Image(systemName: "checkmark.circle.fill")
                .font(.title2)
                .foregroundColor(.green)
        }
    }
    
    var body: some View {
        Group {
            if let fileURL = fileURL {
                ShareLink(item: fileURL) {
                    downloadRowContent
                }
                .foregroundStyle(.primary)
            } else {
                // Fallback if file URL is not available
                Button(action: {
                    VideoDownloadService.shared.openFilesApp()
                }) {
                    downloadRowContent
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.vertical, 4)
        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
            Button("Delete", role: .destructive) {
                onDelete()
            }
        }
        .contextMenu {
            Button(action: onDelete) {
                Label("Delete", systemImage: "trash")
            }
        }
    }
}

#Preview("Downloads View") {
    DownloadsView()
}

#Preview("Active Download Row - Downloading") {
    let sampleVideo = UIVideo(
        id: 12345,
        uid: "sample-uid",
        title: "Sample Video Title That Is Very Long To Test Truncation In Download Rows",
        imageUrl: URL(string: "https://picsum.photos/400/250"),
        duration: 3600,
        timestamp: Date().timeIntervalSince1970 - 86400,
        category: "Gaming",
        channel: UIChannel(
            id: "sample-channel",
            name: "Sample Channel",
            imageUrl: URL(string: "https://picsum.photos/200")
        ),
        progress: nil
    )

    let sampleQuality = VideoQuality(
        id: "1080p",
        resolution: 1080,
        bandwidth: 5000000,
        url: URL(string: "https://example.com/video.m3u8")!
    )

    let downloadInfo = VideoDownloadInfo(
        video: sampleVideo,
        quality: sampleQuality,
        downloadURL: URL(string: "https://example.com/download.ts")!,
        destinationURL: URL(string: "file:///tmp/video.ts")!,
        state: .downloading(progress: 0.65),
        startTime: Date().addingTimeInterval(-300),
        totalBytes: 1024 * 1024 * 500, // 500MB
        downloadedBytes: 1024 * 1024 * 325 // 325MB downloaded
    )

    ActiveDownloadRow(download: downloadInfo) {
        print("Cancel tapped")
    }
    .padding()
}

#Preview("Active Download Row - Processing") {
    let sampleVideo = UIVideo(
        id: 12346,
        uid: "sample-uid-2",
        title: "Processing Video",
        imageUrl: URL(string: "https://picsum.photos/400/250"),
        duration: 1800,
        timestamp: Date().timeIntervalSince1970 - 43200,
        category: "Music",
        channel: UIChannel(
            id: "music-channel",
            name: "Music Channel",
            imageUrl: URL(string: "https://picsum.photos/200")
        ),
        progress: nil
    )

    let sampleQuality = VideoQuality(
        id: "720p",
        resolution: 720,
        bandwidth: 3000000,
        url: URL(string: "https://example.com/video.m3u8")!
    )

    let downloadInfo = VideoDownloadInfo(
        video: sampleVideo,
        quality: sampleQuality,
        downloadURL: URL(string: "https://example.com/download.ts")!,
        destinationURL: URL(string: "file:///tmp/video.ts")!,
        state: .processing,
        startTime: Date().addingTimeInterval(-600),
        totalBytes: 1024 * 1024 * 300, // 300MB
        downloadedBytes: 1024 * 1024 * 300 // Fully downloaded, now processing
    )

    ActiveDownloadRow(download: downloadInfo) {
        print("Cancel tapped")
    }
    .padding()
}

#Preview("Active Download Row - Pending") {
    let sampleVideo = UIVideo(
        id: 12347,
        uid: "sample-uid-3",
        title: "Pending Download",
        imageUrl: URL(string: "https://picsum.photos/400/250"),
        duration: 2400,
        timestamp: Date().timeIntervalSince1970 - 7200,
        category: "Entertainment",
        channel: UIChannel(
            id: "entertainment-channel",
            name: "Entertainment Channel",
            imageUrl: URL(string: "https://picsum.photos/200")
        ),
        progress: nil
    )

    let sampleQuality = VideoQuality(
        id: "480p",
        resolution: 480,
        bandwidth: 1500000,
        url: URL(string: "https://example.com/video.m3u8")!
    )

    let downloadInfo = VideoDownloadInfo(
        video: sampleVideo,
        quality: sampleQuality,
        downloadURL: URL(string: "https://example.com/download.ts")!,
        destinationURL: URL(string: "file:///tmp/video.ts")!,
        state: .pending,
        startTime: Date(),
        totalBytes: 1024 * 1024 * 200, // 200MB
        downloadedBytes: 0
    )

    ActiveDownloadRow(download: downloadInfo) {
        print("Cancel tapped")
    }
    .padding()
}

#Preview("Completed Download Row") {
    let sampleVideo = UIVideo(
        id: 12348,
        uid: "sample-uid-4",
        title: "Completed Download Video With Very Long Title That Should Wrap",
        imageUrl: URL(string: "https://picsum.photos/400/250"),
        duration: 4200,
        timestamp: Date().timeIntervalSince1970 - 172800,
        category: "Sports",
        channel: UIChannel(
            id: "sports-channel",
            name: "Sports Channel",
            imageUrl: URL(string: "https://picsum.photos/200")
        ),
        progress: nil
    )

    let sampleQuality = VideoQuality(
        id: "1080p",
        resolution: 1080,
        bandwidth: 5000000,
        url: URL(string: "https://example.com/video.m3u8")!
    )

    let downloadInfo = VideoDownloadInfo(
        video: sampleVideo,
        quality: sampleQuality,
        downloadURL: URL(string: "https://example.com/download.ts")!,
        destinationURL: URL(string: "file:///tmp/completed_video.ts")!,
        state: .completed(URL(string: "file:///tmp/completed_video.ts")!),
        startTime: Date().addingTimeInterval(-3600),
        totalBytes: 1024 * 1024 * 800, // 800MB
        downloadedBytes: 1024 * 1024 * 800 // Fully downloaded
    )

    CompletedDownloadRow(
        download: downloadInfo,
        fileURL: URL(string: "file:///tmp/completed_video.ts")!
    ) {
        print("Delete tapped")
    }
    .padding()
}

#Preview("Completed Download Row - No File URL") {
    let sampleVideo = UIVideo(
        id: 12349,
        uid: "sample-uid-5",
        title: "Completed Download Without File URL",
        imageUrl: URL(string: "https://picsum.photos/400/250"),
        duration: 1500,
        timestamp: Date().timeIntervalSince1970 - 259200,
        category: "Education",
        channel: UIChannel(
            id: "edu-channel",
            name: "Education Channel",
            imageUrl: URL(string: "https://picsum.photos/200")
        ),
        progress: nil
    )

    let sampleQuality = VideoQuality(
        id: "720p",
        resolution: 720,
        bandwidth: 3000000,
        url: URL(string: "https://example.com/video.m3u8")!
    )

    let downloadInfo = VideoDownloadInfo(
        video: sampleVideo,
        quality: sampleQuality,
        downloadURL: URL(string: "https://example.com/download.ts")!,
        destinationURL: URL(string: "file:///tmp/missing_video.ts")!,
        state: .completed(URL(string: "file:///tmp/missing_video.ts")!),
        startTime: Date().addingTimeInterval(-7200),
        totalBytes: 1024 * 1024 * 450, // 450MB
        downloadedBytes: 1024 * 1024 * 450 // Fully downloaded
    )

    CompletedDownloadRow(
        download: downloadInfo,
        fileURL: nil // No file URL available
    ) {
        print("Delete tapped")
    }
    .padding()
}
#endif
