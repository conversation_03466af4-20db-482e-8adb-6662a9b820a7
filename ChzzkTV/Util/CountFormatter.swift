import Foundation

enum CountFormatter {
    /// Formats a count into an abbreviated string using K/M thresholds.
    /// - Parameters:
    ///   - count: The integer count to format.
    ///   - locale: Locale to use for any locale-specific tweaks. Defaults to current locale.
    /// - Returns: A formatted string such as "999", "1.2K", "12K", or "1.2M".
    static func abbreviated(from count: Int, locale: Locale = .current) -> String {
        if count < 1000 {
            return String(format: "%lld", count)
        } else if count < 10000 {
            let formatted = Double(count) / 1000.0
            return String(format: NSLocalizedString("%.1fK", comment: "thousands unit"), formatted)
        } else if count < 1_000_000 {
            var formatted = Double(count) / 1000.0
            // Preserve original behavior for Korean language users
            let languageCode = locale.language.languageCode?.identifier
            if languageCode == "ko" {
                formatted = formatted / 10.0
            }
            return String(format: NSLocalizedString("%.0fK", comment: "thousands unit"), formatted)
        } else {
            let formatted = Double(count) / 1_000_000.0
            return String(format: NSLocalizedString("%.1fM", comment: "millions unit"), formatted)
        }
    }
} 