//
//  ImageCacheManager.swift
//  ChzzkTV
//
//  Created by Augment Agent on 8/27/25.
//

import Foundation
import UIKit
import os.log
import CryptoKit

/// A disk-based image cache manager for storing and retrieving images
final class ImageCacheManager: @unchecked Sendable {
    static let shared = ImageCacheManager()
    
    private let logger = Logger(subsystem: "ChzzkTV", category: "ImageCache")
    private let fileManager = FileManager.default
    private let cacheDirectory: URL
    private let maxCacheSize: Int = 100 * 1024 * 1024 // 100MB
    private let maxCacheAge: TimeInterval = 7 * 24 * 60 * 60 // 7 days
    
    // In-memory cache for recently accessed images
    private let memoryCache = NSCache<NSString, UIImage>()
    private let cacheQueue = DispatchQueue(label: "com.chzzkTV.imageCache", qos: .utility)
    
    private init() {
        // Create cache directory
        let cachesDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
        cacheDirectory = cachesDirectory.appendingPathComponent("ImageCache")
        
        // Configure memory cache
        memoryCache.countLimit = 50 // Limit to 50 images in memory
        memoryCache.totalCostLimit = 20 * 1024 * 1024 // 20MB memory limit
        
        createCacheDirectoryIfNeeded()
        cleanupExpiredFiles()
    }
    
    // MARK: - Public Methods
    
    /// Retrieves an image from cache (memory first, then disk)
    func getImage(for url: URL) async -> UIImage? {
        let cacheKey = cacheKey(for: url)
        
        // Check memory cache first
        if let cachedImage = memoryCache.object(forKey: cacheKey as NSString) {
            return cachedImage
        }
        
        // Check disk cache
        return await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: nil)
                    return
                }
                
                let fileURL = self.fileURL(for: cacheKey)
                
                guard self.fileManager.fileExists(atPath: fileURL.path) else {
                    continuation.resume(returning: nil)
                    return
                }
                
                // Check if file is expired
                if self.isFileExpired(fileURL) {
                    self.removeFile(at: fileURL)
                    continuation.resume(returning: nil)
                    return
                }
                
                guard let data = try? Data(contentsOf: fileURL),
                      let image = UIImage(data: data) else {
                    self.logger.warning("Failed to load image data from cache for URL: \(url.absoluteString)")
                    continuation.resume(returning: nil)
                    return
                }
                
                // Store in memory cache for future access
                let cost = data.count
                self.memoryCache.setObject(image, forKey: cacheKey as NSString, cost: cost)
                
                continuation.resume(returning: image)
            }
        }
    }
    
    /// Stores an image in cache (both memory and disk)
    func storeImage(_ image: UIImage, for url: URL) async {
        let cacheKey = cacheKey(for: url)
        
        await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                guard let data = image.jpegData(compressionQuality: 0.8) else {
                    self.logger.warning("Failed to convert image to JPEG data for URL: \(url.absoluteString)")
                    continuation.resume()
                    return
                }
                
                let fileURL = self.fileURL(for: cacheKey)
                
                do {
                    try data.write(to: fileURL)
                    
                    // Store in memory cache
                    let cost = data.count
                    self.memoryCache.setObject(image, forKey: cacheKey as NSString, cost: cost)
                } catch {
                    self.logger.error("Failed to write image to cache: \(error.localizedDescription)")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// Downloads and caches an image from the given URL
    func downloadAndCacheImage(from url: URL) async -> UIImage? {
        // Check if already cached
        if let cachedImage = await getImage(for: url) {
            return cachedImage
        }
        
        // Download image
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            guard let image = UIImage(data: data) else {
                logger.warning("Failed to create image from downloaded data for URL: \(url.absoluteString)")
                return nil
            }
            
            // Store in cache
            await storeImage(image, for: url)
            
//            logger.debug("Image downloaded and cached for URL: \(url.absoluteString)")
            return image
        } catch {
            logger.error("Failed to download image from URL \(url.absoluteString): \(error.localizedDescription)")
            return nil
        }
    }
    
    /// Clears all cached images
    func clearCache() async {
        await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                do {
                    let files = try self.fileManager.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: nil)
                    for file in files {
                        try self.fileManager.removeItem(at: file)
                    }
                    
                    self.memoryCache.removeAllObjects()
                    self.logger.info("Cache cleared successfully")
                } catch {
                    self.logger.error("Failed to clear cache: \(error.localizedDescription)")
                }
                
                continuation.resume()
            }
        }
    }

    /// Removes an image from both memory and disk cache
    /// - Parameter url: The URL of the image to remove from cache
    func removeImage(for url: URL) async {
        let cacheKey = cacheKey(for: url)
        let fileURL = fileURL(for: cacheKey)

        await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }

                // Remove from memory cache
                self.memoryCache.removeObject(forKey: cacheKey as NSString)

                // Remove from disk cache
                self.removeFile(at: fileURL)

                self.logger.debug("Removed image from cache: \(cacheKey)")
                continuation.resume()
            }
        }
    }

    // MARK: - Private Methods
    
    private func createCacheDirectoryIfNeeded() {
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            do {
                try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
                logger.debug("Cache directory created at: \(self.cacheDirectory.path)")
            } catch {
                logger.error("Failed to create cache directory: \(error.localizedDescription)")
            }
        }
    }
    
    private func cacheKey(for url: URL) -> String {
        // Create a safe filename by using SHA256 hash of the URL
        guard let data = url.absoluteString.data(using: .utf8) else {
            return "unknown"
        }

        // Use SHA256 to create a consistent, filesystem-safe filename
        let hash = SHA256.hash(data: data)

        // Convert to hex string (safe for filenames) and add .jpg extension
        return hash.compactMap { String(format: "%02x", $0) }.joined() + ".jpg"
    }
    
    private func fileURL(for cacheKey: String) -> URL {
        return cacheDirectory.appendingPathComponent(cacheKey)
    }
    
    private func isFileExpired(_ fileURL: URL) -> Bool {
        guard let attributes = try? fileManager.attributesOfItem(atPath: fileURL.path),
              let modificationDate = attributes[.modificationDate] as? Date else {
            return true
        }
        
        return Date().timeIntervalSince(modificationDate) > maxCacheAge
    }
    
    private func removeFile(at url: URL) {
        do {
            try fileManager.removeItem(at: url)
            logger.debug("Expired cache file removed: \(url.lastPathComponent)")
        } catch {
            logger.warning("Failed to remove expired cache file: \(error.localizedDescription)")
        }
    }
    
    private func cleanupExpiredFiles() {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            
            do {
                let files = try self.fileManager.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: [.contentModificationDateKey, .fileSizeKey])
                
                var totalSize: Int64 = 0
                var fileInfos: [(url: URL, date: Date, size: Int64)] = []
                
                for file in files {
                    let attributes = try file.resourceValues(forKeys: [.contentModificationDateKey, .fileSizeKey])
                    let date = attributes.contentModificationDate ?? Date.distantPast
                    let size = Int64(attributes.fileSize ?? 0)
                    
                    if self.isFileExpired(file) {
                        self.removeFile(at: file)
                    } else {
                        fileInfos.append((url: file, date: date, size: size))
                        totalSize += size
                    }
                }
                
                // Remove oldest files if cache size exceeds limit
                if totalSize > self.maxCacheSize {
                    fileInfos.sort { $0.date < $1.date }
                    
                    for fileInfo in fileInfos {
                        if totalSize <= self.maxCacheSize { break }
                        
                        self.removeFile(at: fileInfo.url)
                        totalSize -= fileInfo.size
                    }
                }
                
                self.logger.debug("Cache cleanup completed. Total size: \(totalSize) bytes")
            } catch {
                self.logger.error("Failed to cleanup cache: \(error.localizedDescription)")
            }
        }
    }
}
