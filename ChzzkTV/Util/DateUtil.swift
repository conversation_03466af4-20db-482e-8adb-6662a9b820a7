//
//  DateUtil.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/21/25.
//

import Foundation

extension Date {
    func relativeString() -> String {
        let now = Date()
        let calendar = Calendar.current
        let components = calendar.dateComponents([.second], from: self, to: now)
        
        // If less than 10 seconds, show "Just now"
        if let seconds = components.second, seconds < 10 {
            return NSLocalizedString("Just now", comment: "Time indicator for very recent content")
        }
        
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        return formatter.localizedString(for: self, relativeTo: now)
    }
}
