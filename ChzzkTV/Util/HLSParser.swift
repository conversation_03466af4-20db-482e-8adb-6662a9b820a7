//
//  HLSParser.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/12/25.
//

import Foundation

struct HLSParser {
    func masterUrl(from mediaList: [Media]) throws -> URL {
        var mediaPath: String?
        
        // First try to get LLHLS
        if let llhlsMedia = mediaList.first(where: { $0.mediaId == "LLHLS" }) {
            mediaPath = llhlsMedia.path
        }
        
        // Fall back to HLS if LLHLS is not available
        if mediaPath == nil, let hlsMedia = mediaList.first(where: { $0.mediaId == "HLS" }) {
            mediaPath = hlsMedia.path
        }
        
        if let mediaPath = mediaPath, let masterUrl = URL(string: mediaPath) {
            return masterUrl
        } else {
            throw NSError(domain: "HLSParser", code: -4, userInfo: [NSLocalizedDescriptionKey: "Failed to find the master playlist URL"])
        }
    }
    
    func availableQualities(from masterUrl: URL) async throws -> [VideoQuality] {
        let (data, _) = try await URLSession.shared.data(from: masterUrl)
        guard let content = String(data: data, encoding: .utf8) else {
            throw NSError(domain: "HLSParser", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to decode playlist"])
        }
        
        var variants: [VideoQuality] = []
        let lines = content.components(separatedBy: .newlines)
        
        for (index, line) in lines.enumerated() {
            if line.hasPrefix("#EXT-X-STREAM-INF:"), index + 1 < lines.count {
                let urlString = lines[index + 1].trimmingCharacters(in: .whitespacesAndNewlines)
                if urlString.isEmpty { continue }
                
                // Extract resolution from the STREAM-INF line
                var resolution = 0
                if let resolutionRange = line.range(of: "RESOLUTION=\\d+x(\\d+)", options: .regularExpression) {
                    let resolutionString = String(line[resolutionRange])
                    if let heightRange = resolutionString.range(of: "\\d+$", options: .regularExpression) {
                        resolution = Int(resolutionString[heightRange]) ?? 0
                    }
                }
                
                // Extract bandwidth information as fallback for resolution
                if resolution == 0, let bandwidthRange = line.range(of: "BANDWIDTH=(\\d+)", options: .regularExpression) {
                    let bandwidthString = String(line[bandwidthRange])
                    if let valueRange = bandwidthString.range(of: "\\d+", options: .regularExpression),
                       let bandwidth = Int(bandwidthString[valueRange]) {
                        // Rough estimate of resolution based on bandwidth
                        if bandwidth > 4500000 {
                            resolution = 1080
                        } else if bandwidth > 2500000 {
                            resolution = 720
                        } else if bandwidth > 1400000 {
                            resolution = 480
                        } else if bandwidth > 800000 {
                            resolution = 360
                        } else {
                            resolution = 240
                        }
                    }
                }
                
                if let variantUrl = urlString.hasPrefix("http")
                    ? URL(string: urlString)
                    : URL(string: urlString, relativeTo: masterUrl)?.absoluteURL {
                    variants.append(VideoQuality(
                        id: "\(resolution)",
                        resolution: resolution,
                        bandwidth: 0,
                        url: variantUrl
                    ))
                }
            }
        }
        
        // If empty, return the master URL
        if variants.isEmpty {
            throw NSError(domain: "HLSParser", code: -2, userInfo: [NSLocalizedDescriptionKey: "variants are empty"])
        }
        
        // Sort variants by resolution (descending)
        let sortedVariants = variants.sorted { $0.resolution > $1.resolution }
        
        return sortedVariants
    }
    
    func getBestQualityVariant(from masterUrl: URL) async throws -> URL {
        let sortedVariants = try await self.availableQualities(from: masterUrl)
        
        // Find 1080p variant if available
        if let variant1080p = sortedVariants.first(where: { $0.resolution >= 1080 }) {
            print("Selected 1080p+ stream: \(variant1080p.resolution)p")
            return variant1080p.url
        }
        
        // Otherwise return the highest resolution available
        guard let highestVariant = sortedVariants.first else {
            throw NSError(domain: "HLSParser", code: -3, userInfo: [NSLocalizedDescriptionKey : "No suitable variant found"])
        }
        
        print("Selected highest available stream: \(highestVariant.resolution)p")
        return highestVariant.url
    }
}
