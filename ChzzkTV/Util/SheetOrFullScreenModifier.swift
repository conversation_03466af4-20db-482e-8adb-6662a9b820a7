//
//  SheetOrFullScreenModifier.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/14/25.
//

import SwiftUI

struct SheetOrFullScreenModifier<SheetContent: View>: ViewModifier {
    @Binding var isPresented: Bool
    let onDismiss: (() -> Void)?
    let content: () -> SheetContent

    func body(content base: Content) -> some View {
        #if os(tvOS)
        base.fullScreenCover(isPresented: $isPresented, onDismiss: onDismiss, content: self.content)
        #else
        base.sheet(isPresented: $isPresented, onDismiss: onDismiss, content: self.content)
        #endif
    }
}

extension View {
    func sheetOrFullScreen<SheetContent: View>(
        isPresented: Binding<Bool>,
        onDismiss: (() -> Void)? = nil,
        @ViewBuilder content: @escaping () -> SheetContent
    ) -> some View {
        self.modifier(SheetOrFullScreenModifier(isPresented: isPresented, onDismiss: onDismiss, content: content))
    }
} 