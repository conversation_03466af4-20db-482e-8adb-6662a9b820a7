import SwiftUI

/// Utility for detecting screen characteristics and device types
/// 
/// This utility provides methods to detect:
/// - Small screens (iOS + portrait mode)
/// - Portrait vs landscape orientation
/// - iOS vs other platforms
/// - Screen dimensions and thresholds
///
/// ## Usage Examples:
///
/// ### Basic screen detection:
/// ```swift
/// // Detect if current screen is small (iOS + portrait)
/// let isSmall = ScreenDetection.isSmallScreen()
///
/// // Detect if device is in portrait mode
/// let isPortrait = ScreenDetection.isPortraitMode()
///
/// // Detect if device is iOS
/// let isIOS = ScreenDetection.isIOS()
/// ```
///
/// ### Using with environment values:
/// ```swift
/// struct MyView: View {
///     @Environment(\.horizontalSizeClass) private var horizontalSizeClass
///     
///     var body: some View {
///         if ScreenDetection.isSmallScreen(horizontalSizeClass: horizontalSizeClass) {
///             // Small screen layout
///         } else {
///             // Large screen layout
///         }
///     }
/// }
/// ```
///
/// ### Using convenience methods:
/// ```swift
/// struct MyView: View {
///     var body: some View {
///         if isSmallScreen() {
///             // Small screen layout
///         } else {
///             // Large screen layout
///         }
///     }
/// }
/// ```
struct ScreenDetection {
    
    /// Detects if the current screen is a small screen (iOS + portrait mode)
    /// - Parameter horizontalSizeClass: The horizontal size class from the environment
    /// - Parameter verticalSizeClass: The vertical size class from the environment
    /// - Returns: True if the screen is small (iOS + portrait mode)
    static func isSmallScreen(
        horizontalSizeClass: UserInterfaceSizeClass? = nil,
        verticalSizeClass: UserInterfaceSizeClass? = nil
    ) -> Bool {
        #if os(iOS)
        // For iOS, we need to consider both size classes and actual screen dimensions
        // since horizontalSizeClass is always .compact on iOS devices in portrait mode
        
        // First, check if we're in portrait mode using screen dimensions
        let isPortrait = isPortraitMode()
        
        // If we're in portrait mode, check the screen width
        if isPortrait {
            let screenWidth = getScreenWidth()
            // Consider screens smaller than 600 points as small screens
            // This covers iPhone in portrait mode (typically 375-430 points)
            return screenWidth < 600
        }
        
        // If we're in landscape mode, check the horizontal size class
        if let horizontalSizeClass = horizontalSizeClass {
            return horizontalSizeClass == .compact
        }
        
        // Fallback: check screen dimensions
        let screenWidth = getScreenWidth()
        return screenWidth < 600
        #else
        return false // Not iOS, so not a small screen
        #endif
    }
    
    /// Detects if the current device is in portrait mode
    /// - Returns: True if the device is in portrait mode
    static func isPortraitMode() -> Bool {
        #if os(iOS)
        // Use UIDevice orientation as a more reliable method
        let orientation = UIDevice.current.orientation
        switch orientation {
        case .portrait, .portraitUpsideDown:
            return true
        case .landscapeLeft, .landscapeRight:
            return false
        default:
            // If orientation is unknown, check screen dimensions
            let screenSize = UIScreen.main.bounds.size
            return screenSize.height > screenSize.width
        }
        #else
        return false
        #endif
    }
    
    /// Detects if the current device is iOS
    /// - Returns: True if the device is iOS
    static func isIOS() -> Bool {
        #if os(iOS)
        return true
        #else
        return false
        #endif
    }
    
    /// Detects if the current screen is a small iOS screen in portrait mode
    /// - Returns: True if it's iOS + portrait mode (small screen)
    static func isSmallIOSPortraitScreen() -> Bool {
        return isIOS() && isPortraitMode()
    }
    
    /// Gets the current screen size
    /// - Returns: The current screen size as CGSize
    static func getScreenSize() -> CGSize {
        #if os(iOS)
        return UIScreen.main.bounds.size
        #else
        return CGSize.zero
        #endif
    }
    
    /// Gets the current screen width
    /// - Returns: The current screen width
    static func getScreenWidth() -> CGFloat {
        return getScreenSize().width
    }
    
    /// Gets the current screen height
    /// - Returns: The current screen height
    static func getScreenHeight() -> CGFloat {
        return getScreenSize().height
    }
    
    /// Checks if the screen width is less than a given threshold
    /// - Parameter threshold: The width threshold to check against (default: 600)
    /// - Returns: True if the screen width is less than the threshold
    static func isScreenWidthLessThan(_ threshold: CGFloat = 600) -> Bool {
        return getScreenWidth() < threshold
    }
    
    /// Detects if the current device is an iPhone (small screen device)
    /// - Returns: True if the device is an iPhone
    static func isiPhone() -> Bool {
        #if os(iOS)
        return UIDevice.current.userInterfaceIdiom == .phone
        #else
        return false
        #endif
    }
    
    /// Detects if the current device is an iPad (large screen device)
    /// - Returns: True if the device is an iPad
    static func isiPad() -> Bool {
        #if os(iOS)
        return UIDevice.current.userInterfaceIdiom == .pad
        #else
        return false
        #endif
    }
    
    /// More sophisticated small screen detection that considers device type and orientation
    /// - Returns: True if the screen should be considered small
    static func isSmallScreenAdvanced() -> Bool {
        #if os(iOS)
        // If it's an iPhone, it's always a small screen
        if isiPhone() {
            return true
        }
        
        // If it's an iPad, check orientation and size
        if isiPad() {
            let isPortrait = isPortraitMode()
            let screenWidth = getScreenWidth()
            
            // iPad in portrait mode with width less than 600 is considered small
            if isPortrait && screenWidth < 600 {
                return true
            }
            
            // iPad in landscape mode is generally not small
            return false
        }
        
        // Fallback: check screen dimensions
        return getScreenWidth() < 600
        #else
        return false
        #endif
    }
    
    /// Simple and reliable small screen detection for iOS
    /// - Returns: True if the screen should be considered small
    static func isSmallScreenSimple() -> Bool {
        #if os(iOS)
        // Check if it's an iPhone first (most reliable)
        if isiPhone() {
            return true
        }
        
        // For iPad, check if it's in portrait mode and has a small width
        if isiPad() {
            let screenWidth = getScreenWidth()
            let isPortrait = isPortraitMode()
            
            // iPad in portrait mode with width less than 600 is considered small
            return isPortrait && screenWidth < 600
        }
        
        // Fallback: any screen with width less than 600 is small
        return getScreenWidth() < 600
        #else
        return false
        #endif
    }
    
    /// Most reliable small screen detection - uses device type as primary indicator
    /// - Returns: True if the screen should be considered small
    static func isSmallScreenReliable() -> Bool {
        #if os(iOS)
        // iPhone is always considered small screen
        if isiPhone(), isPortraitMode() {
            return true
        }
        
        // iPad is considered small screen only in portrait mode with narrow width
        if isiPad() {
            let screenWidth = getScreenWidth()
            let screenHeight = getScreenHeight()
            
            // Check if it's in portrait mode (height > width) and width is small
            if screenHeight > screenWidth && screenWidth < 600 {
                return true
            }
            
            // iPad in landscape mode is not small
            return false
        }
        
        // For other devices, check screen width
        return getScreenWidth() < 600
        #else
        return false
        #endif
    }
    
    /// Debug method to print all screen detection information
    /// - Returns: A string with all the debug information
    static func debugInfo() -> String {
        #if os(iOS)
        let isIOS = self.isIOS()
        let isiPhone = self.isiPhone()
        let isiPad = self.isiPad()
        let isPortrait = isPortraitMode()
        let screenWidth = getScreenWidth()
        let screenHeight = getScreenHeight()
        let isSmallScreenSimple = isSmallScreenSimple()
        let isSmallScreenAdvanced = isSmallScreenAdvanced()
        let isSmallScreenReliable = isSmallScreenReliable()
        
        return """
        Screen Detection Debug Info:
        - Is iOS: \(isIOS)
        - Is iPhone: \(isiPhone)
        - Is iPad: \(isiPad)
        - Is Portrait: \(isPortrait)
        - Screen Width: \(screenWidth)
        - Screen Height: \(screenHeight)
        - Is Small Screen (Simple): \(isSmallScreenSimple)
        - Is Small Screen (Advanced): \(isSmallScreenAdvanced)
        - Is Small Screen (Reliable): \(isSmallScreenReliable)
        """
        #else
        return "Not iOS platform"
        #endif
    }
}

/// Extension to provide screen detection as an environment value
struct ScreenDetectionKey: EnvironmentKey {
    static let defaultValue: ScreenDetection = ScreenDetection()
}

extension EnvironmentValues {
    var screenDetection: ScreenDetection {
        get { self[ScreenDetectionKey.self] }
        set { self[ScreenDetectionKey.self] = newValue }
    }
}

/// Extension to provide convenient screen detection methods for views
extension View {
    /// Detects if the current view is on a small screen (iOS + portrait mode)
    /// - Returns: True if the screen is small
    func isSmallScreen() -> Bool {
        #if os(iOS)
        return true // Default to small screen for iOS
        #else
        return false
        #endif
    }
    
    /// Detects if the current view is on iOS
    /// - Returns: True if the device is iOS
    func isIOS() -> Bool {
        return ScreenDetection.isIOS()
    }
    
    /// Detects if the current view is in portrait mode
    /// - Returns: True if the device is in portrait mode
    func isPortraitMode() -> Bool {
        return ScreenDetection.isPortraitMode()
    }
}
