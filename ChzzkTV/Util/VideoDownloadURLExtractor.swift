//
//  VideoDownloadURLExtractor.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import Foundation
import os.log

/// Utility class for extracting downloadable URLs from video content
/// This class is owned by the VideoDownloadService and handles the complex logic
/// of converting streaming URLs to downloadable formats
class VideoDownloadURLExtractor {
    private let logger = Logger(subsystem: "ChzzkTV", category: "VideoDownloadURLExtractor")
    private let networkService: NetworkService
    
    init(networkService: NetworkService = NetworkService.shared) {
        self.networkService = networkService
    }
    
    /// Extracts a downloadable URL from a video quality
    /// For HLS streams, this processes the M3U8 playlist to get segment URLs
    func extractDownloadURL(from quality: VideoQuality) async throws -> URL {
        let url = quality.url
        
        // Check if this is an HLS stream (M3U8)
        if url.pathExtension.lowercased() == "m3u8" || url.absoluteString.contains(".m3u8") {
            return try await processHLSPlaylist(url)
        }
        
        // For direct video URLs, return as-is
        return url
    }
    
    /// Processes an HLS playlist to extract the best quality stream URL
    private func processHLSPlaylist(_ playlistURL: URL) async throws -> URL {
        logger.debug("Processing HLS playlist: \(playlistURL.absoluteString)")

        do {
            // Download the playlist content
            let (data, response) = try await URLSession.shared.data(from: playlistURL)

            // Check response status
            if let httpResponse = response as? HTTPURLResponse,
               !(200...299).contains(httpResponse.statusCode) {
                logger.error("Failed to fetch playlist, status code: \(httpResponse.statusCode)")
                throw VideoDownloadError.invalidURL
            }

            guard let playlistContent = String(data: data, encoding: .utf8) else {
                logger.error("Failed to decode playlist content as UTF-8")
                throw VideoDownloadError.invalidURL
            }

            logger.debug("Playlist content length: \(playlistContent.count) characters")

            // Parse the playlist
            let lines = playlistContent.components(separatedBy: .newlines)
            var bestStreamURL: URL?
            var highestBandwidth = 0
            var isMediaPlaylist = false

            // Check if this is a media playlist (contains segments) vs master playlist (contains variants)
            for line in lines {
                if line.hasPrefix("#EXTINF:") {
                    isMediaPlaylist = true
                    break
                }
            }

            if isMediaPlaylist {
                // This is already a media playlist, return the original URL
                logger.debug("Detected media playlist, using original URL")
                return playlistURL
            }

            // Process master playlist to find best variant
            for (index, line) in lines.enumerated() {
                if line.hasPrefix("#EXT-X-STREAM-INF:") {
                    // Extract bandwidth information
                    let bandwidth = extractBandwidth(from: line)
                    logger.debug("Found stream variant with bandwidth: \(bandwidth)")

                    // Get the stream URL from the next line
                    if index + 1 < lines.count {
                        let streamURLString = lines[index + 1].trimmingCharacters(in: .whitespacesAndNewlines)
                        if !streamURLString.isEmpty && !streamURLString.hasPrefix("#") {
                            let streamURL = streamURLString.hasPrefix("http")
                                ? URL(string: streamURLString)
                                : URL(string: streamURLString, relativeTo: playlistURL)?.absoluteURL

                            if let streamURL = streamURL, bandwidth > highestBandwidth {
                                bestStreamURL = streamURL
                                highestBandwidth = bandwidth
                                logger.debug("New best stream: \(streamURL.absoluteString)")
                            }
                        }
                    }
                }
            }

            guard let bestURL = bestStreamURL else {
                // If no stream variants found, use the original URL
                logger.debug("No variants found, using original playlist URL")
                return playlistURL
            }

            logger.info("Selected best stream with bandwidth: \(highestBandwidth)")
            return bestURL

        } catch {
            logger.error("Error processing HLS playlist: \(error.localizedDescription)")
            // Fallback to original URL if processing fails
            return playlistURL
        }
    }
    
    /// Extracts bandwidth information from an EXT-X-STREAM-INF line
    private func extractBandwidth(from line: String) -> Int {
        // Look for BANDWIDTH=xxxxxx pattern
        if let range = line.range(of: "BANDWIDTH=(\\d+)", options: .regularExpression) {
            let bandwidthString = String(line[range])
            if let valueRange = bandwidthString.range(of: "\\d+", options: .regularExpression) {
                return Int(bandwidthString[valueRange]) ?? 0
            }
        }
        return 0
    }
    
    /// Validates if a URL is downloadable
    func isDownloadable(_ url: URL) async -> Bool {
        do {
            var request = URLRequest(url: url)
            request.httpMethod = "HEAD"
            request.timeoutInterval = 10
            
            let (_, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                // Check if the response indicates a downloadable resource
                let statusCode = httpResponse.statusCode
                let contentType = httpResponse.value(forHTTPHeaderField: "Content-Type")?.lowercased()
                
                // Accept 2xx status codes
                guard 200...299 ~= statusCode else {
                    logger.warning("URL not downloadable - status code: \(statusCode)")
                    return false
                }
                
                // Check for video content types
                if let contentType = contentType {
                    let videoTypes = ["video/", "application/vnd.apple.mpegurl", "application/x-mpegurl"]
                    let isVideoContent = videoTypes.contains { contentType.contains($0) }
                    
                    if !isVideoContent {
                        logger.warning("URL not downloadable - content type: \(contentType)")
                        return false
                    }
                }
                
                return true
            }
            
            return false
        } catch {
            logger.error("Failed to validate URL: \(error.localizedDescription)")
            return false
        }
    }
    
    /// Gets the estimated file size for a video URL
    func getEstimatedFileSize(for url: URL) async -> Int64? {
        do {
            var request = URLRequest(url: url)
            request.httpMethod = "HEAD"
            request.timeoutInterval = 10
            
            let (_, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse,
               let contentLengthString = httpResponse.value(forHTTPHeaderField: "Content-Length"),
               let contentLength = Int64(contentLengthString) {
                return contentLength
            }
            
            return nil
        } catch {
            logger.error("Failed to get file size: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// Formats file size for display
    static func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    /// Estimates download time based on file size and connection speed
    static func estimateDownloadTime(fileSize: Int64, connectionSpeedBytesPerSecond: Int64 = 1_000_000) -> TimeInterval {
        guard connectionSpeedBytesPerSecond > 0 else { return 0 }
        return Double(fileSize) / Double(connectionSpeedBytesPerSecond)
    }
    
    /// Formats time interval for display
    static func formatTimeInterval(_ interval: TimeInterval) -> String {
        let hours = Int(interval) / 3600
        let minutes = Int(interval) % 3600 / 60
        let seconds = Int(interval) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
}
