import Foundation

class DeviceIdManager {
    static let shared = DeviceIdManager()
    
    private let userDefaults = UserDefaults.standard
    private let deviceIdKey = "DeviceId"
    
    private init() {}
    
    var deviceId: String {
        if let existingDeviceId = userDefaults.string(forKey: deviceIdKey) {
            return existingDeviceId
        } else {
            let newDeviceId = UUID().uuidString
            userDefaults.set(newDeviceId, forKey: deviceIdKey)
            return newDeviceId
        }
    }
    
    func resetDeviceId() {
        userDefaults.removeObject(forKey: deviceIdKey)
    }
} 