//
//  ChzzkResourceLoaderDelegate.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/26/25.
//

import AVKit

class ChzzkResourceLoaderDelegate: NSObject, AVAssetResourceLoaderDelegate {
    let queryItems: [URLQueryItem]
    
    init(queryItems: [URLQueryItem]) {
        self.queryItems = queryItems
    }
    
    func resourceLoader(
        _ resourceLoader: AVAssetResourceLoader,
        shouldWaitForLoadingOfRequestedResource loadingRequest: AVAssetResourceLoadingRequest
    ) -> Bool {
        guard let url = loadingRequest.request.url else { return false }
        return handleResourceRequest(url, loadingRequest)
    }
    
    func resourceLoader(
        _ resourceLoader: AVAssetResourceLoader,
        shouldWaitForRenewalOfRequestedResource renewalRequest: AVAssetResourceRenewalRequest
    ) -> Bool {
        guard let url = renewalRequest.request.url else { return false }
        return handleResourceRequest(url, renewalRequest)
    }
    
    private func handleResourceRequest(_ url: URL, _ loadingRequest: AVAssetResourceLoadingRequest) -> Bool {
        switch url.pathExtension {
        case "m3u8":
            return handleM3U8Request(url, loadingRequest)
        case "ts":
            return handleTSRequest(url, loadingRequest)
        default:
            return false
        }
    }
    
    private func handleM3U8Request(_ url: URL, _ loadingRequest: AVAssetResourceLoadingRequest) -> Bool {
        // Convert to HTTPS URL
        guard let httpsURL = convertToHTTPS(url) else { return false }
        
        let task = URLSession.shared.dataTask(with: httpsURL) { [weak self] data, response, error in
            guard let self = self else { return }
            
            if let error = error {
                loadingRequest.finishLoading(with: error)
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200,
                  let data = data,
                  let m3u8String = String(data: data, encoding: .utf8) else {
                loadingRequest.finishLoading(with: NSError(domain: "", code: -1))
                return
            }
            
            self.handleM3U8Content(m3u8String, baseURL: httpsURL, loadingRequest: loadingRequest)
        }
        
        task.resume()
        return true
    }
    
    private func handleM3U8Content(
        _ content: String,
        baseURL: URL,
        loadingRequest: AVAssetResourceLoadingRequest
    ) {
        let baseURLWithoutFile = baseURL.deletingLastPathComponent()
        
        // Modify M3U8 content to include authenticated TS URLs
        let modifiedLines = content.components(separatedBy: .newlines).map { line -> String in
            guard line.hasSuffix(".ts") else { return line }
            
            let tsURL = baseURLWithoutFile.appendingPathComponent(line)
            var tsComponents = URLComponents(url: tsURL, resolvingAgainstBaseURL: true)!
            tsComponents.queryItems = queryItems
            
            return tsComponents.url?.absoluteString ?? line
        }
        
        let modifiedM3u8 = modifiedLines.joined(separator: "\n")
        
        let modifiedData = modifiedM3u8.data(using: .utf8)!
        
        // Set content information and respond
        loadingRequest.contentInformationRequest?.contentType = "application/x-mpegURL"
        loadingRequest.contentInformationRequest?.contentLength = Int64(modifiedData.count)
        loadingRequest.contentInformationRequest?.isByteRangeAccessSupported = true
        
        loadingRequest.dataRequest?.respond(with: modifiedData)
        loadingRequest.finishLoading()
    }
    
    private func handleTSRequest(_ url: URL, _ loadingRequest: AVAssetResourceLoadingRequest) -> Bool {
        // Convert to HTTPS URL with GDA token
        guard var components = URLComponents(url: url, resolvingAgainstBaseURL: true) else { return false }
        
        components.scheme = "https"
        components.queryItems = queryItems
        
        guard let modifiedURL = components.url else { return false }
               
        // Set up redirection
        let response = HTTPURLResponse(
            url: url,
            statusCode: 302,
            httpVersion: nil,
            headerFields: [
                "Location": modifiedURL.absoluteString,
                "Content-Type": "video/MP2T"
            ]
        )
        
        var redirectRequest = URLRequest(url: modifiedURL)
        redirectRequest.setValue("*/*", forHTTPHeaderField: "Accept")
        redirectRequest.setValue("video/MP2T", forHTTPHeaderField: "Content-Type")
        
        loadingRequest.redirect = redirectRequest
        loadingRequest.response = response
        loadingRequest.contentInformationRequest?.contentType = "video/MP2T"
        loadingRequest.contentInformationRequest?.isByteRangeAccessSupported = true
        
        loadingRequest.finishLoading()
        return true
    }
    
    private func convertToHTTPS(_ url: URL) -> URL? {
        var components = URLComponents(url: url, resolvingAgainstBaseURL: true)
        components?.scheme = "https"
        return components?.url
    }
}
