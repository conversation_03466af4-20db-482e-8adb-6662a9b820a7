import Foundation

class CategoryService: CategoryServiceProtocol {
    private let networkService: NetworkService
    private var nextCategory: Next?
    private var nextLive: Next?
    
    public init(networkService: NetworkService = NetworkService.shared) {
        self.networkService = networkService
    }
    
    func getCategories(usePagination: Bool = false) async throws -> ([UICategory], Bool) {
        let url: String
        
        if usePagination, let next = self.nextCategory {
            url = "\(APIEndpoint.categories)?concurrentUserCount=\(next.concurrentUserCount ?? 0)&categoryId=\(next.categoryId ?? "")&size=16&openLiveCount=\(next.openLiveCount ?? 0)"
        } else {
            url = APIEndpoint.categories
        }
        
        let response: CategoryResponse = try await networkService.request(url: url)
        
        let categories = response.content.data.map { $0.toUICategory() }
        
        self.nextCategory = response.content.page?.next
        
        return (categories, self.nextCategory != nil)
    }
    
    func getLivesInCategory(_ category: UICategory, usePagination: Bool = false) async throws -> ([UILiveStream], Bool) {
        var url = "\(APIEndpoint.categoryLives)/\(category.type)/\(category.id)/lives?"
        
        if usePagination, let next = self.nextLive {
            url = url + "concurrentUserCount=\(next.concurrentUserCount ?? 0)&liveId=\(next.liveId ?? 0)"
        }
        
        let response: LiveStreamResponse = try await networkService.request(url: url)
        
        let categories = response.content.data.map { $0.toUILiveStream() }
        
        self.nextLive = response.content.page?.next
        
        return (categories, self.nextCategory != nil)
    }
}
