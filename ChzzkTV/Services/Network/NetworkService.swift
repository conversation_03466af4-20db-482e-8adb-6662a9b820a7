import Foundation

enum NetworkError: Error {
    case invalidURL
    case invalidResponse
    case decodingFailed
    case decodingError(Error)
    case serverError(Int)
    case unknown
}

class NetworkService {
    static let shared = NetworkService()
    private let session: URLSession
    private let logger = NetworkLogger.shared
    
    private init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 300
        session = URLSession(configuration: config)
    }
    
    func request<T: Decodable>(
        url: String,
        method: String = "GET",
        headers: [String: String] = ["Content-Type": "application/json"],
        body: Data? = nil
    ) async throws -> T {
        guard let url = URL(string: url) else {
            throw NetworkError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method
        request.allHTTPHeaderFields = headers
        request.httpBody = body
        
        do {
            let (data, response) = try await session.data(for: request)
            
            // Log response
            logger.logResponse(response, data: data, error: nil)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            switch httpResponse.statusCode {
            case 200...299:
                do {
                    return try JSONDecoder().decode(T.self, from: data)
                } catch {
                    throw NetworkError.decodingError(error)
                }
            case 400...499:
                throw NetworkError.serverError(httpResponse.statusCode)
            case 500...599:
                throw NetworkError.serverError(httpResponse.statusCode)
            default:
                throw NetworkError.unknown
            }
        } catch {
            logger.logResponse(nil, data: nil, error: error)
            throw error
        }
    }
    
    func request(
        url: String,
        method: String = "GET",
        headers: [String: String] = ["Content-Type": "application/json"],
        body: Data? = nil
    ) async throws -> [String: Any]? {
        guard let url = URL(string: url) else {
            throw NetworkError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method
        request.allHTTPHeaderFields = headers
        request.httpBody = body
        
        do {
            let (data, response) = try await session.data(for: request)
            
            // Log response
            logger.logResponse(response, data: data, error: nil)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            switch httpResponse.statusCode {
            case 200...299:
                do {
                    if data.isEmpty {
                        return nil
                    }
                    guard let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                        throw NetworkError.decodingFailed
                    }
                    return jsonObject
                } catch {
                    throw NetworkError.decodingError(error)
                }
            case 400...499:
                throw NetworkError.serverError(httpResponse.statusCode)
            case 500...599:
                throw NetworkError.serverError(httpResponse.statusCode)
            default:
                throw NetworkError.unknown
            }
        } catch {
            logger.logResponse(nil, data: nil, error: error)
            throw error
        }
    }
}
