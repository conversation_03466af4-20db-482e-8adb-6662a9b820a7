import Foundation
import os.log

class NetworkLogger {
    static let shared = NetworkLogger()
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "koo.jaesung.chzzktv", category: "Network")
    
    private init() {}
    
    func logResponse(_ response: URLResponse?, data: Data?, error: Error?) {
        guard let httpResponse = response as? HTTPURLResponse else {
            if let error = error {
                logger.error("❌ Network Error: \(error)")
            }
            return
        }
        
        let url = httpResponse.url?.absoluteString ?? "Unknown"
        let statusCode = httpResponse.statusCode
        var jsonDic: [String: Any]?
        if let data = data, let json = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
            jsonDic = json
        }
        
        if (200...299).contains(statusCode) {
            logger.info("""
            ✅ \(statusCode) \(url)
            """)
        } else {
            logger.error("""
            ❌ \(statusCode) \(url) 
            \(String(describing: jsonDic))
            """)
        }
    }
} 
