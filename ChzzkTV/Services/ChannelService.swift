import Foundation

class ChannelService: ChannelServiceProtocol {
    private let networkService: NetworkService
    private var followingChannels: [UIChannel] = []

    public init(networkService: NetworkService = NetworkService.shared) {
        self.networkService = networkService
    }
    
    func getChannel(id: String) async throws -> UIChannel {
        let response: ChannelResponse = try await networkService.request(
            url: "\(APIEndpoint.channel)/\(id)"
        )
        return response.content.toUIChannel()
    }
    
    func getLive(id: String) async throws -> UILiveStream? {
        let response: ChannelDataResponse = try await networkService.request(
            url: "\(APIEndpoint.channelData)/\(id)/data?fields=topExposedVideos"
        )
        
        return response.content?.topExposedVideos?.openLive?.toUILiveStream()
    }
    
    func getLatestVideo(id: String) async throws -> UIVideo? {
        let response: ChannelDataResponse = try await networkService.request(
            url: "\(APIEndpoint.channelData)/\(id)/data?fields=topExposedVideos"
        )
        
        return response.content?.topExposedVideos?.latestVideo?.toUIVideo()
    }

    func getVideos(id: String, page: Int = 0, size: Int = 16) async throws -> ([UIVideo]?, Int?) {
        let response: VideoListResponse = try await networkService.request(
            url: "\(APIEndpoint.channelVideos)/\(id)/videos?sortType=LATEST&pagingType=PAGE&page=\(page)&size=\(size)"
        )
        let videos = response.content?.data
            .filter({ $0.adult == false && $0.blindType == nil })
            .map({ $0.toUIVideo() })
        return (videos, response.content?.totalPages)
    }
    
    func getFollowingChannels() async throws -> [UIChannel] {
        let response: FollowingChannelResponse = try await networkService.request(
            url: APIEndpoint.followingChannels
        )
        
        followingChannels = response.content.followingList.map { $0.channel.toUIChannel() }
        
        return followingChannels
    }
    
    func toggleFollow(channelId: String) async -> Bool {
        do {
            if isFollowing(channelId: channelId) {
                try await unfollow(channelId: channelId)
                return false
            } else {
                try await follow(channelId: channelId)
                return true
            }
        } catch {
            return false
        }
    }
    
    func isFollowing(channelId: String) -> Bool {
        return followingChannels.first { $0.id == channelId } != nil
    }
    
    func getLiveWithPlaybackUrl(id: String) async throws -> UILiveStream? {
        let response: LiveStreamDetailResponse = try await networkService.request(
            url: "\(APIEndpoint.liveDetail)/\(id)/live-detail"
        )
        
        let liveStream = response.content
        
        let parser = HLSParser()
        let masterUrl = try parser.masterUrl(from: liveStream.livePlayback().media)
        let qualities = try await parser.availableQualities(from: masterUrl)
        
        return liveStream.toUILiveStream(qualities: qualities)
    }
}

extension ChannelService {
    func follow(channelId: String) async throws {
        let response: FollowResponse = try await networkService.request(
            url: "\(APIEndpoint.channel)/\(channelId)/follow",
            method: "POST"
        )
        
        if let channel = response.content?.channel {
            followingChannels.append(channel.toUIChannel())
        }
    }
    
    func unfollow(channelId: String) async throws {
        let response: FollowResponse = try await networkService.request(
            url: "\(APIEndpoint.channel)/\(channelId)/follow",
            method: "DELETE"
        )
        
        if response.message == "SUCCESS", let index = followingChannels.firstIndex(where: { $0.id == channelId }) {
            followingChannels.remove(at: index)
        }
    }
}
