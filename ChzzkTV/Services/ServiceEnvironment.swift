import SwiftUI

// MARK: - Environment Keys

struct ChannelServiceKey: EnvironmentKey {
    static let defaultValue: ChannelServiceProtocol = ChannelService()
}

struct VideoServiceKey: EnvironmentKey {
    static let defaultValue: VideoServiceProtocol = VideoService()
}

struct LiveServiceKey: EnvironmentKey {
    static let defaultValue: LiveServiceProtocol = LiveService()
}

struct SearchServiceKey: EnvironmentKey {
    static let defaultValue: SearchServiceProtocol = SearchService()
}

// Add a new key for CategoryService
private struct CategoryServiceKey: EnvironmentKey {
    static let defaultValue: CategoryServiceProtocol = CategoryService()
}

// MARK: - Environment Values Extension

extension EnvironmentValues {
    var channelService: ChannelServiceProtocol {
        get { self[ChannelServiceKey.self] }
        set { self[ChannelServiceKey.self] = newValue }
    }
    
    var videoService: VideoServiceProtocol {
        get { self[VideoServiceKey.self] }
        set { self[VideoServiceKey.self] = newValue }
    }
    
    var liveService: LiveServiceProtocol {
        get { self[LiveServiceKey.self] }
        set { self[LiveServiceKey.self] = newValue }
    }
    
    var searchService: SearchServiceProtocol {
        get { self[SearchServiceKey.self] }
        set { self[SearchServiceKey.self] = newValue }
    }
    
    var categoryService: CategoryServiceProtocol {
        get { self[CategoryServiceKey.self] }
        set { self[CategoryServiceKey.self] = newValue }
    }
}
