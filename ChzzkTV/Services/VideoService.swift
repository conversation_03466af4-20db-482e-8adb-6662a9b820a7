//
//  VideoService.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/30/25.
//

import AVKit

enum VideoPlayEvent: String {
    case started = "WATCH_STARTED"
    case continued = "WATCH_CONTINUED"
    case ended = "WATCH_ENDED"
}

class VideoService: VideoServiceProtocol {
    private let networkService: NetworkService
    private var resourceDelegate: AVAssetResourceLoaderDelegate?
    
    private var response: VideoDetail?
    private var availableQualities: [VideoQuality]?
    
    public init(networkService: NetworkService = NetworkService.shared) {
        self.networkService = networkService
    }
    
    public func getVideo(_ videoId: Int) async throws -> UIVideo {
        let detailResponse: VideoDetailResponse = try await networkService.request(
            url: "\(APIEndpoint.vodDetail)/\(videoId)"
        )
        
        let video = detailResponse.content
        response = video
        
        // Pre-fetch available qualities
        if let videoUid = video.videoId, let inKey = video.inKey {
            _ = try await getVODQualities(id: videoId, uid: videoUid, inKey: inKey)
        } else if let liveRewindPlayback = video.liveRewindPlayback {
            _ = try await getLiveRewindQualities(id: videoId, from: liveRewindPlayback)
        }
        
        return video.toUIVideo()
    }
    
    func getFollowingVideos() async throws -> [UIVideo] {
        let response: FollowingVideoResponse = try await networkService.request(
            url: APIEndpoint.followingVideos
        )
        
        return response.content.data.map { $0.toUIVideo() }
    }
    
    func watchEvent(time: Int, video: UIVideo, event: VideoPlayEvent) async throws {
        let requestBody = [
            "channelId": video.channel?.id ?? "",
            "videoNo": video.id,
            "totalLength": video.duration,
            "payload": [
                "duration": 10,
                "positionAt": time,
                "watchEventType": event.rawValue
            ]
        ] as [String : Any]
        
        let data = try JSONSerialization.data(withJSONObject: requestBody)
        
        _ = try await networkService.request(
            url: APIEndpoint.videoWatchEvent,
            method: "POST",
            headers: ["Content-Type": "application/json", "Deviceid": DeviceIdManager.shared.deviceId],
            body: data
        )
    }
    
    func getAvailableQualities(for videoId: Int) async throws -> [VideoQuality] {
        // Return cached qualities if available
        if let qualities = availableQualities {
            return qualities
        }
        
        // If we don't have the video details yet, fetch them
        if response == nil {
            _ = try await getVideo(videoId)
            if let qualities = availableQualities {
                return qualities
            }
        }
        
        // If we still don't have qualities, something went wrong
        throw VideoDetailError.unknownPlaybackResponse
    }
    
    func createAsset(for videoId: Int, quality: VideoQuality) async throws -> AVURLAsset {
        var video: VideoDetail?
        
        if response != nil {
            video = response
        } else {
            let detailResponse: VideoDetailResponse = try await networkService.request(
                url: "\(APIEndpoint.vodDetail)/\(videoId)"
            )
            
            video = detailResponse.content
        }
        
        guard let video else {
            throw VideoDetailError.videoIdNotFound
        }
        
        if let inKey = video.inKey {
            return try await createVODAsset(inKey: inKey, quality: quality)
        } else if let liveRewindPlayback = video.liveRewindPlayback {
            return try await createLiveRewindAsset(from: liveRewindPlayback, quality: quality)
        } else {
            throw VideoDetailError.unknownPlaybackResponse
        }
    }
    
    func clear() {
        resourceDelegate = nil
        response = nil
        availableQualities = nil
    }
    
    private func getVODQualities(id: Int, uid: String, inKey: String) async throws -> [VideoQuality] {
        print("Getting VOD qualities for videoId: \(uid)")
        
        let detailUrl = "\(APIEndpoint.vodPlaybackV2)/\(uid)?key=\(inKey)"
        print("Fetching playback details from: \(detailUrl)")
        
        guard let jsonObject = try await networkService.request(url: detailUrl) else {
            print("Failed to get playback details from server")
            throw VideoDetailError.unknownPlaybackResponse
        }
        
        let representations = try representationSet(from: jsonObject)
        print("Found \(representations.count) quality representations")
        
        var qualities: [VideoQuality] = []
        
        for representation in representations {
            guard let otherAttributes = representation["otherAttributes"] as? [String: Any],
                  let m3u8URLString = otherAttributes["m3u"] as? String,
                  let m3u8URL = URL(string: m3u8URLString),
                  let bandwidth = representation["bandwidth"] as? Int,
                  let height = representation["height"] as? Int else {
                print("Failed to extract quality information from representation")
                continue
            }
            
            let quality = VideoQuality(
                id: "\(uid)_\(height)",
                resolution: height,
                bandwidth: bandwidth,
                url: m3u8URL
            )
            
            print("Added quality: \(quality.displayName) (bandwidth: \(bandwidth))")
            qualities.append(quality)
        }
        
        // Sort qualities by resolution (descending)
        qualities.sort { $0.resolution > $1.resolution }
        
        // Cache the qualities
        availableQualities = qualities
        print("Cached \(qualities.count) qualities for video \(id)")
        
        return qualities
    }
    
    private func getLiveRewindQualities(id: Int, from playback: LiveRewindPlayback) async throws -> [VideoQuality] {
        print("Getting live rewind qualities")
        print("Available media: \(playback.media.map { $0.mediaId })")
        
        let parser = HLSParser()
        let masterUrl = try parser.masterUrl(from: playback.media)
        let qualities = try await parser.availableQualities(from: masterUrl)
        
        availableQualities = qualities
        
        print("Cached \(qualities.count) qualities for video \(id)")
        
        return qualities
    }
    
    private func createVODAsset(inKey: String, quality: VideoQuality) async throws -> AVURLAsset {
        print("Creating VOD asset with quality: \(quality.displayName)")
        
        return try createCustomAsset(from: quality.url)
    }
    
    private func createLiveRewindAsset(from playback: LiveRewindPlayback, quality: VideoQuality) async throws -> AVURLAsset {
        print("Creating live rewind asset with quality: \(quality.displayName)")
        
        return AVURLAsset(url: quality.url)
    }
    
    private func createCustomAsset(from m3u8URL: URL) throws -> AVURLAsset {
        guard let queryItems = extractQueryItems(from: m3u8URL) else {
            throw VideoDetailError.invalidGdaParameter
        }
        
        var components = URLComponents(url: m3u8URL, resolvingAgainstBaseURL: false)!
        components.scheme = "chzzk"
        
        guard let customURL = components.url else {
            throw VideoDetailError.invalidUrl
        }
        
        let asset = AVURLAsset(url: customURL)
        self.resourceDelegate = ChzzkResourceLoaderDelegate(queryItems: queryItems)
        asset.resourceLoader.setDelegate(self.resourceDelegate, queue: .main)
        
        return asset
    }
    
    private func representationSet(from json: [String: Any]) throws -> [[String: Any]] {
        guard let periods = json["period"] as? [[String: Any]],
              let firstPeriod = periods.first else {
            throw VideoPlaybackError.missingVideoData("period not found")
        }
        
        guard let adaptationSets = firstPeriod["adaptationSet"] as? [[String: Any]] else {
            throw VideoPlaybackError.missingVideoData("adaptationSet not found")
        }
        
        var videoAdaptation: [String: Any]?
        for adaptation in adaptationSets {
            if let mimeType = adaptation["mimeType"] as? String,
               mimeType.contains("video/mp2t") {
                videoAdaptation = adaptation
                break
            }
        }
        
        guard let videoAdaptation = videoAdaptation else {
            throw VideoPlaybackError.missingVideoData("video/mp2t mime type not found")
        }
        
        guard let representations = videoAdaptation["representation"] as? [[String: Any]] else {
            throw VideoPlaybackError.missingVideoData("representation not found")
        }
        
        return representations
    }
    
    private func extractHighestQualityM3U8URL(from representations: [[String: Any]]) throws -> URL {
        let sortedRepresentations = representations.sorted {
            let bandwidth1 = $0["bandwidth"] as? Int ?? 0
            let bandwidth2 = $1["bandwidth"] as? Int ?? 0
            return bandwidth1 > bandwidth2
        }
        
        guard let highestQuality = sortedRepresentations.first else {
            throw VideoPlaybackError.missingVideoData("sorted representations array is empty")
        }
             
        // Try to get M3U8 URL from otherAttributes
        guard let otherAttributes = highestQuality["otherAttributes"] as? [String: Any],
              let m3u8URLString = otherAttributes["m3u"] as? String,
              let m3u8URL = URL(string: m3u8URLString) else {
            throw VideoPlaybackError.invalidM3U8URL
        }
        
        print("M3U8 URL: \(m3u8URL.absoluteString)")
        
        return m3u8URL
    }
    
    private func extractQueryItems(from url: URL) -> [URLQueryItem]? {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false) else {
            return nil
        }
        
        return components.queryItems
    }
}
