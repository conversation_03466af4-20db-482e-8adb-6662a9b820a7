//
//  LiveService.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/30/25.
//

import Foundation

class LiveService: LiveServiceProtocol {
    private let networkService: NetworkService
    private var next: Next?
    private var imageWidth = 720
    
    public init(networkService: NetworkService = NetworkService.shared) {
        self.networkService = networkService
#if os(iOS)
        imageWidth = 480
#endif
    }
    
    func getAllLives(type: LiveSortType) async throws -> ([UILiveStream], Bool) {
        let response: LiveStreamResponse = try await networkService.request(
            url: "\(APIEndpoint.allLives)?sortType=\(type.rawValue)"
        )
        
        let streams = response.content.data
            .map({ $0.toUILiveStream(size: imageWidth) })
        
        self.next = response.content.page?.next
        
        return (streams, self.next != nil)
    }
    
    func getNextAllLives(type: LiveSortType) async throws -> ([UILiveStream], Bool) {
        guard let next = self.next else {
            return try await getAllLives(type: type)
        }
        
        let url = "\(APIEndpoint.allLives)?concurrentUserCount=\(next.concurrentUserCount ?? 0)&liveId=\(next.liveId ?? 0)&size=20&sortType=\(type.rawValue)"
        let response: LiveStreamResponse = try await networkService.request(url: url)
        
        let streams = response.content.data
            .map({ $0.toUILiveStream(size: imageWidth) })
        
        self.next = response.content.page?.next
        
        return (streams, self.next != nil)
    }
    
    func getFollowingLives() async throws -> [UILiveStream] {
        let response: FollowingLiveResponse = try await networkService.request(
            url: APIEndpoint.followingLives
        )
        
        return response.content.followingList
            .map({ follow in
                var live = follow.liveInfo.toUILiveStream(size: imageWidth)
                live.channel = follow.channel.toUIChannel()
                
                return live
            })
    }
}
